import request from "@/services/request/request.ts";
import type {UpdateRoleRes} from "@/services/role.ts";

// Response interface
export interface GetAllRes {
    /* */
    code: number;

    /* */
    data: {
        /* */
        id: number;

        /* */
        pid: number;

        /* */
        menuName: string;

        /* */
        route: string;

        /* */
        createTime: Record<string, unknown>;

        /* */
        updateTime: Record<string, unknown>;
    }[];

    /* */
    message: string;

    /* */
    description: string;
}

/**
 * getAll
 * @returns
 */
export function getAll(): Promise<GetAllRes> {
    return request.get(`/api/menu/getAll`).then(
        (res: { data: GetAllRes }) => res.data
    );
}

// Response interface
export interface GetByRoleIdRes {
    /* */
    code: number;

    /* */
    data: {
        /* */
        id: number;

        /* */
        pid: number;

        /* */
        menuName: string;

        /* */
        route: string;

        /* */
        createTime: Record<string, unknown>;

        /* */
        updateTime: Record<string, unknown>;
    }[];

    /* */
    message: string;

    /* */
    description: string;
}

/**
 * getByRoleId
 * @param {string} roleId
 * @returns
 */
export function getByRoleId(roleId: number): Promise<GetByRoleIdRes> {
    return request.get(`/api/menu/getByRoleId?roleId=${roleId}`).then(
        (res: { data: GetByRoleIdRes }) => res.data
    );
}

// Response interface
export interface GetAllByUserIdRes {
    /* */
    code: number;

    /* */
    data: {
        /* */
        id: number;

        /* */
        pid: number;

        /* */
        menuName: string;

        /* */
        route: string;

        /* */
        createTime: Record<string, unknown>;

        /* */
        updateTime: Record<string, unknown>;
    }[];

    /* */
    message: string;

    /* */
    description: string;
}

/**
 * getAllByUserId
 * @param {string} userId
 * @returns
 */
export function getAllByUserId(userId: number): Promise<GetAllByUserIdRes> {
    return request.get(`/api/menu/getMenu?userId=${userId}`).then(
        (res: { data: GetAllByUserIdRes }) => res.data
    );
}

// Parameter interface
export interface UpdateRoleMenusParams {
    /* */
    roleId?: number;

    /* */
    menuIds?: number[];
}

// Response interface
export interface UpdateRoleMenusRes {
    /* */
    code: number;

    /* */
    data: Record<string, unknown>;

    /* */
    message: string;

    /* */
    description: string;
}

/**
 * updateRoleMenus
 * @param {object} params RoleMenuDTO
 * @param {number} params.roleId
 * @param {array} params.menuIds
 * @returns
 */
export function updateRoleMenus(params: UpdateRoleMenusParams): Promise<UpdateRoleMenusRes> {
    return request.post(`/api/menu/updateRoleMenus`, params).then(
        (res: { data: UpdateRoleMenusRes }) => res.data
    );
}
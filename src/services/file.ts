import request from "@/services/request/request";

// Response interface
export interface UploadFileRes {
    /* */
    code: number;

    /* */
    data: string;

    /* */
    message: string;

    /* */
    description: string;
}

/**
 * uploadFile
 * @param {File} file
 * @returns
 */
export function uploadFile(file: File): Promise<UploadFileRes> {
    const formData = new FormData()
    formData.append('file', file)
    
    return request.post('/api/oss/uploadFile', formData, {
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    }).then(
        (res: { data: UploadFileRes }) => res.data
    )
}
import request from "@/services/request/request.ts";

//查询订单
export interface QueryOrdersPageParams {
    /* */
    pageNum?: number;

    /* */
    pageSize?: number;

    /* */
    sort?: {
        /* */
        column?: string;

        /* */
        asc?: boolean;
    };

    /* */
    params?: {
        /* */
        id?: number;

        /* */
        ids?: Record<string, unknown>[];

        /* */
        userId?: number;

        /* */
        contactPhone?: string;

        /*可用值:pending,paid,completed,cancelled */
        status?: string;

        /* */
        createTime?: Date;

        /* */
        startDate?: Date;

        /* */
        endDate?: Date;

        /* */
        orderNumber?: string;

        /* */
        carId?: number;

        /* */
        carName?: string;

        /* */
        isDelete?: number;

        /* */
        includeDeleted?: boolean;
    };
}

export interface QueryOrdersPageRes {
    /* */
    code: number;

    /* */
    data: {
        /* */
        pageNum: number;

        /* */
        pageSize: number;

        /* */
        total: number;

        /* */
        totalPage: number;

        /* */
        list: {
            /* */
            id: number;

            /* */
            orderNumber: string;

            /* */
            userId: number;

            /* */
            carId: number;

            /* */
            carName: string;

            /* */
            startDate: Record<string, unknown>;

            /* */
            endDate: Record<string, unknown>;

            /* */
            totalDays: number;

            /* */
            dailyRate: number;

            /* */
            totalAmount: number;

            /*可用值:pending,paid,completed,cancelled */
            status: string;

            /* */
            paymentMethod: string;

            /* */
            paymentTime: Record<string, unknown>;

            /* */
            pickupLocation: string;

            /* */
            returnLocation: string;

            /* */
            contactPhone: string;

            /* */
            remark: string;

            /* */
            createdAt: Record<string, unknown>;

            /* */
            updatedAt: Record<string, unknown>;

            /* */
            isDelete: number;
        }[];
    };

    /* */
    message: string;

    /* */
    description: string;
}

export function queryOrdersPage(params: QueryOrdersPageParams): Promise<QueryOrdersPageRes> {
    return request.post(`/api/order/page`, params).then(
        (res: { data: QueryOrdersPageRes }) => res.data);
}

//添加订单
export interface AddOrdersParams {
    /* */
    id?: number;

    /* */
    orderNumber?: string;

    /* */
    userId?: number;

    /* */
    carId?: number;

    /* */
    carName?: string;

    /* */
    startDate?: Date;

    /* */
    endDate?: Date;

    /* */
    totalDays?: number;

    /* */
    dailyRate?: number;

    /* */
    totalAmount?: number;

    /*可用值:pending,paid,completed,cancelled */
    status?: string;

    /* */
    paymentMethod?: string;

    /* */
    paymentTime?: string;

    /* */
    pickupLocation?: string;

    /* */
    returnLocation?: string;

    /* */
    contactPhone?: string;

    /* */
    remark?: string;

    /* */
    createdAt?: Record<string, unknown>;

    /* */
    updatedAt?: Record<string, unknown>;

    /* */
    isDelete?: number;
}

export interface AddOrdersRes {
    /* */
    code: number;

    /* */
    data: Record<string, unknown>;

    /* */
    message: string;

    /* */
    description: string;
}

export function addOrders(params: AddOrdersParams): Promise<AddOrdersRes> {
    return request.post(`/api/order/add`, params).then(
        (res: { data: AddOrdersRes }) => res.data
    );
}

//删除订单
export interface DeleteOrdersRes {
    /* */
    code: number;

    /* */
    data: Record<string, unknown>;

    /* */
    message: string;

    /* */
    description: string;
}

export function deleteOrders(id: number): Promise<DeleteOrdersRes> {
    return request.delete(`/api/order/delete/${id}`).then(
        (res: { data: DeleteOrdersRes }) => res.data
    );
}

//批量删除订单
export interface DeleteOrdersBatchRes {
    /* */
    code: number;

    /* */
    data: Record<string, unknown>;

    /* */
    message: string;

    /* */
    description: string;
}

export function deleteOrdersBatch(params: any[]): Promise<DeleteOrdersBatchRes> {
    return request.post(`/api/order/deleteBatch`, params).then(
        (res: { data: DeleteOrdersBatchRes }) => res.data
    );
}

//修改订单
export interface UpdateOrdersParams {
    /* */
    id?: number;

    /* */
    orderNumber?: string;

    /* */
    userId?: number;

    /* */
    carId?: number;

    /* */
    carName?: string;

    /* */
    startDate?: Date;

    /* */
    endDate?: Date;

    /* */
    totalDays?: number;

    /* */
    dailyRate?: number;

    /* */
    totalAmount?: number;

    /*可用值:pending,paid,completed,cancelled */
    status?: string;

    /* */
    paymentMethod?: string;

    /* */
    paymentTime?: string;

    /* */
    pickupLocation?: string;

    /* */
    returnLocation?: string;

    /* */
    contactPhone?: string;

    /* */
    remark?: string;

    /* */
    createdAt?: Record<string, unknown>;

    /* */
    updatedAt?: Record<string, unknown>;

    /* */
    isDelete?: number;
}

export interface UpdateOrdersRes {
    /* */
    code: number;

    /* */
    data: Record<string, unknown>;

    /* */
    message: string;

    /* */
    description: string;
}

export function updateOrders(params: UpdateOrdersParams): Promise<UpdateOrdersRes> {
    return request.put(`/api/order/update`, params).then(
        (res: { data: UpdateOrdersRes }) => res.data
    );
}

// Parameter interface
export interface CompleteOrdersParams {
    /* */
    id?: number;

    /* */
    orderNumber?: string;

    /* */
    userId?: number;

    /* */
    carId?: number;

    /* */
    carName?: string;

    /* */
    startDate?: Record<string, unknown>;

    /* */
    endDate?: Record<string, unknown>;

    /* */
    totalDays?: number;

    /* */
    dailyRate?: number;

    /* */
    totalAmount?: number;

    /*可用值:pending,paid,completed,cancelled */
    status?: string;

    /* */
    paymentMethod?: string;

    /* */
    paymentTime?: Record<string, unknown>;

    /* */
    pickupLocation?: string;

    /* */
    returnLocation?: string;

    /* */
    contactPhone?: string;

    /* */
    remark?: string;

    /* */
    createdAt?: Record<string, unknown>;

    /* */
    updatedAt?: Record<string, unknown>;

    /* */
    isDelete?: number;
}

// Response interface
export interface CompleteOrdersRes {
    /* */
    code: number;

    /* */
    data: Record<string, unknown>;

    /* */
    message: string;

    /* */
    description: string;
}

/**
 * completeOrders
 * @param {object} params Orders
 * @param {number} params.id
 * @param {string} params.orderNumber
 * @param {number} params.userId
 * @param {number} params.carId
 * @param {string} params.carName
 * @param {object} params.startDate
 * @param {object} params.endDate
 * @param {number} params.totalDays
 * @param {number} params.dailyRate
 * @param {number} params.totalAmount
 * @param {string} params.status 可用值:pending,paid,completed,cancelled
 * @param {string} params.paymentMethod
 * @param {object} params.paymentTime
 * @param {string} params.pickupLocation
 * @param {string} params.returnLocation
 * @param {string} params.contactPhone
 * @param {string} params.remark
 * @param {object} params.createdAt
 * @param {object} params.updatedAt
 * @param {number} params.isDelete
 * @returns
 */
export function completeOrders(params: CompleteOrdersParams): Promise<CompleteOrdersRes> {
    return request.put(`/api/order/complete`, params).then(
        (res: { data: CompleteOrdersRes }) => res.data
    );
}

// Parameter interface
export interface CancelOrdersParams {
    /* */
    id?: number;

    /* */
    orderNumber?: string;

    /* */
    userId?: number;

    /* */
    carId?: number;

    /* */
    carName?: string;

    /* */
    startDate?: Record<string, unknown>;

    /* */
    endDate?: Record<string, unknown>;

    /* */
    totalDays?: number;

    /* */
    dailyRate?: number;

    /* */
    totalAmount?: number;

    /*可用值:pending,paid,completed,cancelled */
    status?: string;

    /* */
    paymentMethod?: string;

    /* */
    paymentTime?: Record<string, unknown>;

    /* */
    pickupLocation?: string;

    /* */
    returnLocation?: string;

    /* */
    contactPhone?: string;

    /* */
    remark?: string;

    /* */
    createdAt?: Record<string, unknown>;

    /* */
    updatedAt?: Record<string, unknown>;

    /* */
    isDelete?: number;
}

// Response interface
export interface CancelOrdersRes {
    /* */
    code: number;

    /* */
    data: Record<string, unknown>;

    /* */
    message: string;

    /* */
    description: string;
}

/**
 * cancelOrders
 * @param {object} params Orders
 * @param {number} params.id
 * @param {string} params.orderNumber
 * @param {number} params.userId
 * @param {number} params.carId
 * @param {string} params.carName
 * @param {object} params.startDate
 * @param {object} params.endDate
 * @param {number} params.totalDays
 * @param {number} params.dailyRate
 * @param {number} params.totalAmount
 * @param {string} params.status 可用值:pending,paid,completed,cancelled
 * @param {string} params.paymentMethod
 * @param {object} params.paymentTime
 * @param {string} params.pickupLocation
 * @param {string} params.returnLocation
 * @param {string} params.contactPhone
 * @param {string} params.remark
 * @param {object} params.createdAt
 * @param {object} params.updatedAt
 * @param {number} params.isDelete
 * @returns
 */
export function cancelOrders(params: CancelOrdersParams): Promise<CancelOrdersRes> {
    return request.put(`/api/order/cancel`, params).then(
        (res: { data: CancelOrdersRes }) => res.data
    );
}

// Parameter interface
export interface PayOrdersParams {
    /* */
    id?: number;

    /* */
    orderNumber?: string;

    /* */
    userId?: number;

    /* */
    carId?: number;

    /* */
    carName?: string;

    /* */
    startDate?: Date;

    /* */
    endDate?: Date;

    /* */
    totalDays?: number;

    /* */
    dailyRate?: number;

    /* */
    totalAmount?: number;

    /*可用值:pending,paid,completed,cancelled */
    status?: string;

    /* */
    paymentMethod?: string;

    /* */
    paymentTime?: string;

    /* */
    pickupLocation?: string;

    /* */
    returnLocation?: string;

    /* */
    contactPhone?: string;

    /* */
    remark?: string;

    /* */
    createdAt?: Date;

    /* */
    updatedAt?: Date;

    /* */
    isDelete?: number;
}

// Response interface
export interface PayOrdersRes {
    /* */
    code: number;

    /* */
    data: Record<string, unknown>;

    /* */
    message: string;

    /* */
    description: string;
}

/**
 * payOrders
 * @param {object} params Orders
 * @param {number} params.id
 * @param {string} params.orderNumber
 * @param {number} params.userId
 * @param {number} params.carId
 * @param {string} params.carName
 * @param {object} params.startDate
 * @param {object} params.endDate
 * @param {number} params.totalDays
 * @param {number} params.dailyRate
 * @param {number} params.totalAmount
 * @param {string} params.status 可用值:pending,paid,completed,cancelled
 * @param {string} params.paymentMethod
 * @param {object} params.paymentTime
 * @param {string} params.pickupLocation
 * @param {string} params.returnLocation
 * @param {string} params.contactPhone
 * @param {string} params.remark
 * @param {object} params.createdAt
 * @param {object} params.updatedAt
 * @param {number} params.isDelete
 * @returns
 */
export function payOrders(params: PayOrdersParams): Promise<PayOrdersRes> {
    return request.put(`/api/order/pay`, params).then(
        (res: { data: PayOrdersRes }) => res.data
    );
}
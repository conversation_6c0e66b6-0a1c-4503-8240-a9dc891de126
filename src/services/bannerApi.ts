// 轮播图接口
export interface Banner {
  id: number
  title: string
  subtitle: string
  description: string
  gradient: string
  image?: string
  buttonText?: string
  status?: number
  sort?: number
  createTime?: Record<string, unknown>
  updateTime?: Record<string, unknown>
  isDelete?: number
}

// 模拟轮播图数据（用于开发阶段）
const mockBanners: Banner[] = [
  {
    id: 1,
    title: '豪华轿车租赁',
    subtitle: '高端出行体验',
    description: '奔驰、宝马、奥迪等豪华车型，让您享受尊贵出行',
    gradient: 'linear-gradient(135deg, rgba(102, 126, 234, 0.3) 0%, rgba(118, 75, 162, 0.3) 100%)',
    image: '/img/banners/banner1.jpg',
    buttonText: '立即租赁',
    status: 1,
    sort: 1
  },
  {
    id: 2,
    title: '新能源汽车',
    subtitle: '绿色环保出行',
    description: '特斯拉、蔚来等新能源车型，科技与环保并存',
    gradient: 'linear-gradient(135deg, rgba(240, 147, 251, 0.3) 0%, rgba(245, 87, 108, 0.3) 100%)',
    image: '/img/banners/banner2.jpg',
    buttonText: '了解更多',
    status: 1,
    sort: 2
  },
  {
    id: 3,
    title: 'SUV越野体验',
    subtitle: '征服每一寸土地',
    description: '适合家庭出游和户外探险的SUV车型',
    gradient: 'linear-gradient(135deg, rgba(79, 172, 254, 0.3) 0%, rgba(0, 242, 254, 0.3) 100%)',
    image: '/img/banners/banner3.jpg',
    buttonText: '开始探索',
    status: 1,
    sort: 3
  },
  {
    id: 4,
    title: '经济实惠选择',
    subtitle: '品质出行不贵',
    description: '高性价比车型，满足日常出行需求',
    gradient: 'linear-gradient(135deg, rgba(67, 233, 123, 0.3) 0%, rgba(56, 249, 215, 0.3) 100%)',
    image: '/img/banners/banner4.jpg',
    buttonText: '查看车型',
    status: 1,
    sort: 4
  },

]

// API
export const bannerApi = {
  // 获取轮播图数据（模拟）
  async getBanners(): Promise<any> {
    try {
      await new Promise(resolve => setTimeout(resolve, 300))
      return mockBanners
    }catch (error) {
      console.warn('error:', error)
    }
  },
} 
import request from "@/services/request/request.ts";
// Parameter interface
export interface ChatParams {
    /* */
    message?: string;

    /* */
    userId?: string;

    /* */
    sessionId?: string;
}

// Response interface
export interface ChatRes {
    /* */
    code: number;

    /* */
    data: string;

    /* */
    message: string;

    /* */
    description: string;
}

/**
 * chat
 * @param {object} params ChatRequest
 * @param {string} params.message
 * @param {string} params.userId
 * @param {string} params.sessionId
 * @returns
 */
export function chat(params: ChatParams): Promise<ChatRes> {
    return request.post(`/api/aiTool/chat`, params).then(
        (res: { data: ChatRes }) => res.data
    );
}
// Parameter interface
export interface ChatStreamParams {
    /* */
    message?: string;

    /* */
    userId?: string;

    /* */
    sessionId?: string;
}

// Response interface
export interface ChatStreamRes {
    /* */
    code: number;

    /* */
    data: string;

    /* */
    message: string;

    /* */
    description: string;
}

/**
 * chatStream
 * @param {object} params ChatRequest
 * @param {string} params.message
 * @param {string} params.userId
 * @param {string} params.sessionId
 * @returns
 */
export function chatStream(params: ChatStreamParams): Promise<Response> {
    return fetch(`${import.meta.env.VITE_API_URL}/api/aiTool/chatStream`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `${sessionStorage.getItem('token')}`
        },
        body: JSON.stringify(params)
    });
}
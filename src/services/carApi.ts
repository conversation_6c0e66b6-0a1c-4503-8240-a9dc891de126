import request from "@/services/request/request.ts";

//查询车辆
export interface QueryCarPageParams {
    /* */
    pageNum?: number;

    /* */
    pageSize?: number;

    /* */
    sort?: {
        /* */
        column?: string;

        /* */
        asc?: boolean;
    };

    /* */
    params?: {
        /* */
        id?: number;

        /* */
        ids?: Record<string, unknown>[];

        /* */
        name?: string;

        /* */
        engine?: string;

        /* */
        seats?: number;

        /* */
        transmission?: string;

        /* */
        dailyRateMin?: number;

        /* */
        dailyRateMax?: number;

        /* */
        brand?: string;

        /* */
        year?: number;

        /* */
        mileageMin?: number;

        /* */
        mileageMax?: number;

        /* */
        fuelType?: string;

        /* */
        available?: number;

        /* */
        createdAtStart?: Record<string, unknown>;

        /* */
        createdAtEnd?: Record<string, unknown>;

        /* */
        updatedAtStart?: Record<string, unknown>;

        /* */
        updatedAtEnd?: Record<string, unknown>;

        /* */
        isDelete?: number;

        /* */
        includeDeleted?: boolean;
    };
}

export interface QueryCarPageRes {
    /* */
    code: number;

    /* */
    data: {
        /* */
        pageNum: number;

        /* */
        pageSize: number;

        /* */
        total: number;

        /* */
        totalPage: number;

        /* */
        list: {
            /* */
            id: number;

            /* */
            name: string;

            /* */
            engine: string;

            /* */
            seats: number;

            /* */
            transmission: string;

            /* */
            dailyRate: number;

            /* */
            brand: string;

            /* */
            year: number;

            /* */
            mileage: number;

            /* */
            fuelType: string;

            /* */
            available: number;

            /* */
            image: string;

            /* */
            createdAt: Record<string, unknown>;

            /* */
            updatedAt: Record<string, unknown>;

            /* */
            isDelete: number;
        }[];
    };

    /* */
    message: string;

    /* */
    description: string;
}

export function queryCarPage(params: QueryCarPageParams): Promise<QueryCarPageRes> {
    return request.post(`/api/car/page`, params).then(
        (res: { data: QueryCarPageRes }) => res.data
    );
}

//id查询车辆
export interface GetCarByIdRes {
    /* */
    code: number;

    /* */
    data: {
        /* */
        id: number;

        /* */
        name: string;

        /* */
        engine: string;

        /* */
        seats: number;

        /* */
        transmission: string;

        /* */
        dailyRate: number;

        /* */
        brand: string;

        /* */
        year: number;

        /* */
        mileage: number;

        /* */
        fuelType: string;

        /* */
        available: number;

        /* */
        image: string;

        /* */
        createdAt: Record<string, unknown>;

        /* */
        updatedAt: Record<string, unknown>;

        /* */
        isDelete: number;
    };

    /* */
    message: string;

    /* */
    description: string;
}

export function getCarById(id: number): Promise<GetCarByIdRes> {
    return request.get(`/api/car/${id}`).then(
        (res: { data: GetCarByIdRes }) => res.data
    );
}

//添加车辆
export interface AddCarParams {
    /* */
    id?: number;

    /* */
    name?: string;

    /* */
    engine?: string;

    /* */
    seats?: number;

    /* */
    transmission?: string;

    /* */
    dailyRate?: number;

    /* */
    brand?: string;

    /* */
    year?: number;

    /* */
    mileage?: number;

    /* */
    fuelType?: string;

    /* */
    available?: number;

    /* */
    image?: string;

    /* */
    createdAt?: Record<string, unknown>;

    /* */
    updatedAt?: Record<string, unknown>;

    /* */
    isDelete?: number;
}

export interface AddCarRes {
    /* */
    code: number;

    /* */
    data: Record<string, unknown>;

    /* */
    message: string;

    /* */
    description: string;
}

export function addCar(params: AddCarParams): Promise<AddCarRes> {
    return request.post(`/api/car/add`, params).then(
        (res: { data: AddCarRes }) => res.data
    );
}

//删除车辆
export interface DeleteCarRes {
    /* */
    code: number;

    /* */
    data: Record<string, unknown>;

    /* */
    message: string;

    /* */
    description: string;
}

export function deleteCar(id: number): Promise<DeleteCarRes> {
    return request.delete(`/api/car/delete/${id}`).then(
        (res: { data: DeleteCarRes }) => res.data
    );
}

//批量删除车辆
export interface DeleteCarBatchRes {
    /* */
    code: number;

    /* */
    data: Record<string, unknown>;

    /* */
    message: string;

    /* */
    description: string;
}

export function deleteCarBatch(params: any[]): Promise<DeleteCarBatchRes> {
    return request.post(`/api/car/deleteBatch`, params).then(
        (res: { data: DeleteCarBatchRes }) => res.data
    );
}

//更新车辆状态
export interface UpdateCarStatusRes {
    /* */
    code: number;

    /* */
    data: Record<string, unknown>;

    /* */
    message: string;

    /* */
    description: string;
}

export function updateCarStatus(id: number, available: number): Promise<UpdateCarStatusRes> {
    return request.put(`/api/car/status/${id}?available=${available}`).then(
        (res: { data: UpdateCarStatusRes }) => res.data
    );
}

//更新车辆
export interface UpdateCarParams {
    /* */
    id?: number;

    /* */
    name?: string;

    /* */
    engine?: string;

    /* */
    seats?: number;

    /* */
    transmission?: string;

    /* */
    dailyRate?: number;

    /* */
    brand?: string;

    /* */
    year?: number;

    /* */
    mileage?: number;

    /* */
    fuelType?: string;

    /* */
    available?: number;

    /* */
    image?: string;

    /* */
    createdAt?: Record<string, unknown>;

    /* */
    updatedAt?: Record<string, unknown>;

    /* */
    isDelete?: number;
}

export interface UpdateCarRes {
    /* */
    code: number;

    /* */
    data: Record<string, unknown>;

    /* */
    message: string;

    /* */
    description: string;
}

export function updateCar(params: UpdateCarParams): Promise<UpdateCarRes> {
    return request.put(`/api/car/update`, params).then(
        (res: { data: UpdateCarRes }) => res.data
    );
}
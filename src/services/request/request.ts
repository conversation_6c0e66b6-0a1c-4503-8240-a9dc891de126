import axios from "axios";
import {ElMessage} from "element-plus";
import router from "@/router/index.js";

// 创建 Axios 实例
const request = axios.create({
    baseURL: `${import.meta.env.VITE_API_URL}`,
    headers: {
        "Content-Type": "application/json",
    },
});
// Axios拦截器处理API返回的Token过期/无效等动态状态
// 请求拦截器
request.interceptors.request.use(config => {
    const token = sessionStorage.getItem('token');
    if (token) {
        config.headers.authorization = token;
    }
    // console.log("config", config);
    return config
});

// 响应拦截器
request.interceptors.response.use(response => {
    const token = response.headers.authorization;
    if (token) {
        sessionStorage.setItem('token', token);
    }
    if (response.data.code === 500) {
        ElMessage.error(response.data.msg);
        router.push('/');
    }
    // console.log("response", response);
    return response
});
export default request;
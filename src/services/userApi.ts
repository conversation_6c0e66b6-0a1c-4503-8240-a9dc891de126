import request from "@/services/request/request.ts";

//用户登录
export interface LoginParams {
    /* */
    id?: number;

    /* */
    username?: string;

    /* */
    userAccount?: string;

    /* */
    avatarUrl?: string;

    /* */
    gender?: number;

    /* */
    userPassword?: string;

    /* */
    phone?: string;

    /* */
    email?: string;

    /* */
    userStatus?: number;

    /* */
    createTime?: Record<string, unknown>;

    /* */
    updateTime?: Record<string, unknown>;

    /* */
    isDelete?: number;

    /* */
    userRole?: number;

    /* */
    lastLoginTime?: Record<string, unknown>;

    /* */
    captcha?: string;

    /* */
    codeId?: string;
}

export interface LoginRes {
    /* */
    code: number;

    /* */
    data: {
        /* */
        id: number;

        /* */
        username: string;

        /* */
        userAccount: string;

        /* */
        avatarUrl: string;

        /* */
        gender: number;

        /* */
        userPassword: string;

        /* */
        phone: string;

        /* */
        email: string;

        /* */
        userStatus: number;

        /* */
        createTime: Record<string, unknown>;

        /* */
        updateTime: Record<string, unknown>;

        /* */
        isDelete: number;

        /* */
        userRole: number;

        /* */
        lastLoginTime: Record<string, unknown>;
    };

    /* */
    message: string;

    /* */
    description: string;
}

export function login(params: LoginParams): Promise<LoginRes> {
    return request.post(`/api/user/login`, params)
        .then((res: { data: LoginRes }) => res.data);
}

//用户注册
export interface RegisterParams {
    /* */
    id?: number;

    /* */
    username?: string;

    /* */
    userAccount?: string;

    /* */
    avatarUrl?: string;

    /* */
    gender?: number;

    /* */
    userPassword?: string;

    /* */
    phone?: string;

    /* */
    email?: string;

    /* */
    userStatus?: number;

    /* */
    createTime?: Record<string, unknown>;

    /* */
    updateTime?: Record<string, unknown>;

    /* */
    isDelete?: number;

    /* */
    userRole?: number;

    /* */
    lastLoginTime?: Record<string, unknown>;

    /* */
    userCheckPassword?: string;
}

export interface RegisterRes {
    /* */
    code: number;

    /* */
    data: Record<string, unknown>;

    /* */
    message: string;

    /* */
    description: string;
}

export function register(params: RegisterParams): Promise<RegisterRes> {
    return request.post(`/api/user/register`, params)
        .then((res: { data: RegisterRes }) => res.data);
}

//获取验证码
export interface GetCodeRes {
    /* */
    code: number;

    /* */
    data: {
        base64Code: string;
        codeId: string;
    }

    /* */
    message: string;

    /* */
    description: string;
}

export function getCode(codeId: string): Promise<GetCodeRes> {
    return request.get(`/api/captcha/getCode?codeId=${codeId}`)
        .then((res: { data: GetCodeRes }) => res.data);
}

//发送邮件
export interface SendSimpleMailRes {
    /* */
    code: number;

    /* */
    data: string;

    /* */
    message: string;

    /* */
    description: string;
}

export function sendSimpleMail(receiveEmail: string): Promise<SendSimpleMailRes> {
    return request.get(`/api/code/email/sendEmail?receiveEmail=${receiveEmail}`)
        .then((res: { data: SendSimpleMailRes }) => res.data);
}

//邮箱登录

export interface EmailLoginParams {
    /* */
    email?: string;

    /* */
    code?: string;
}

export interface EmailLoginRes {
    /* */
    code: number;

    /* */
    data: Record<string, unknown>;

    /* */
    message: string;

    /* */
    description: string;
}

export function emailLogin(params: EmailLoginParams): Promise<EmailLoginRes> {
    return request.post(`/api/code/emailLogin`, params)
        .then((res: { data: EmailLoginRes }) => res.data);
}

//添加用户
export interface AddUserParams {
    /* */
    id?: number;

    /* */
    username?: string;

    /* */
    userAccount?: string;

    /* */
    avatarUrl?: string;

    /* */
    gender?: number;

    /* */
    userPassword?: string;

    /* */
    phone?: string;

    /* */
    email?: string;

    /* */
    userStatus?: number;

    /* */
    createTime?: Record<string, unknown>;

    /* */
    updateTime?: Record<string, unknown>;

    /* */
    isDelete?: number;

    /* */
    userRole?: number;

    /* */
    lastLoginTime?: Record<string, unknown>;
}

export interface AddUserRes {
    /* */
    code: number;

    /* */
    data: Record<string, unknown>;

    /* */
    message: string;

    /* */
    description: string;
}

export function addUser(params: AddUserParams): Promise<AddUserRes> {
    return request.post(`/api/user/add`, params).then(
        (res: { data: AddUserRes }) => res.data
    );
}

//删除用户
export interface DeleteUserRes {
    /* */
    code: number;

    /* */
    data: Record<string, unknown>;

    /* */
    message: string;

    /* */
    description: string;
}

export function deleteUser(id: number): Promise<DeleteUserRes> {
    return request.delete(`/api/user/delete/${id}`).then(
        (res: { data: DeleteUserRes }) => res.data
    );
}

//批量删除用户
export interface DeleteUserBatchRes {
    /* */
    code: number;

    /* */
    data: Record<string, unknown>;

    /* */
    message: string;

    /* */
    description: string;
}


export function deleteUserBatch(params: any[]): Promise<DeleteUserBatchRes> {
    return request.post(`/api/user/deleteBatch`, params).then(
        (res: { data: DeleteUserBatchRes }) => res.data
    );
}

//查看用户日志

export interface QueryUserLogsParams {
    /* */
    action?: string;

    /* */
    timeRange?: number;
}

export interface QueryUserLogsRes {
    /* */
    code: number;

    /* */
    data: {
        /* */
        id: number;

        /* */
        userId: number;

        /* */
        action: string;

        /* */
        description: string;

        /* */
        ipAddress: string;

        /* */
        userAgent: string;

        /* */
        createTime: Record<string, unknown>;

        /* */
        updateTime: Record<string, unknown>;
    }[];

    /* */
    message: string;

    /* */
    description: string;
}


export function queryUserLogs(params: QueryUserLogsParams): Promise<QueryUserLogsRes> {
    return request.post(`/api/user/logs`, params).then(
        (res: { data: QueryUserLogsRes }) => res.data
    );
}

//修改密码
export interface ChangePasswordParams {
    /* */
    id?: number;

    /* */
    oldPassword?: string;

    /* */
    newPassword?: string;

    /* */
    confirmPassword?: string;
}

export interface ChangePasswordRes {
    /* */
    code: number;

    /* */
    data: Record<string, unknown>;

    /* */
    message: string;

    /* */
    description: string;
}

export function changePassword(params: ChangePasswordParams): Promise<ChangePasswordRes> {
    return request.put(`/api/user/password`, params).then(
        (res: { data: ChangePasswordRes }) => res.data
    );
}

//修改用户信息
export interface UpdateUserParams {
    /* */
    id?: number;

    /* */
    username?: string;

    /* */
    userAccount?: string;

    /* */
    avatarUrl?: string;

    /* */
    gender?: number;

    /* */
    userPassword?: string;

    /* */
    phone?: string;

    /* */
    email?: string;

    /* */
    userStatus?: number;

    /* */
    createTime?: Record<string, unknown>;

    /* */
    updateTime?: Record<string, unknown>;

    /* */
    isDelete?: number;

    /* */
    userRole?: number;

    /* */
    lastLoginTime?: Record<string, unknown>;
}

export interface UpdateUserRes {
    /* */
    code: number;

    /* */
    data: Record<string, unknown>;

    /* */
    message: string;

    /* */
    description: string;
}

export function updateUser(params: UpdateUserParams): Promise<UpdateUserRes> {
    return request.put(`/api/user/update`, params).then(
        (res: { data: UpdateUserRes }) => res.data
    );
}

//分页查询用户
export interface QueryUserPageParams {
    /* */
    pageNum?: number;

    /* */
    pageSize?: number;

    /* */
    sort?: {
        /* */
        column?: string;

        /* */
        asc?: boolean;
    };

    /* */
    params?: {
        /* */
        id?: number;

        /* */
        ids?: Record<string, unknown>[];

        /* */
        username?: string;

        /* */
        userAccount?: string;

        /* */
        phone?: string;

        /* */
        email?: string;

        /* */
        userStatus?: number;

        /* */
        userRole?: number;

        /* */
        createTimeStart?: Record<string, unknown>;

        /* */
        createTimeEnd?: Record<string, unknown>;

        /* */
        lastLoginTimeStart?: Record<string, unknown>;

        /* */
        lastLoginTimeEnd?: Record<string, unknown>;

        /* */
        isDelete?: number;

        /* */
        includeDeleted?: boolean;
    };
}

export interface QueryUserPageRes {
    /* */
    code: number;

    /* */
    data: {
        /* */
        pageNum: number;

        /* */
        pageSize: number;

        /* */
        total: number;

        /* */
        totalPage: number;

        /* */
        list: {
            /* */
            id: number;

            /* */
            username: string;

            /* */
            userAccount: string;

            /* */
            avatarUrl: string;

            /* */
            gender: number;

            /* */
            userPassword: string;

            /* */
            phone: string;

            /* */
            email: string;

            /* */
            userStatus: number;

            /* */
            createTime: Record<string, unknown>;

            /* */
            updateTime: Record<string, unknown>;

            /* */
            isDelete: number;

            /* */
            userRole: number;

            /* */
            lastLoginTime: Record<string, unknown>;
        }[];
    };

    /* */
    message: string;

    /* */
    description: string;
}

export function queryUserPage(params: QueryUserPageParams): Promise<QueryUserPageRes> {
    return request.post(`/api/user/page`, params).then(
        (res: { data: QueryUserPageRes }) => res.data
    );
}
import request from "@/services/request/request.ts";

// Parameter interface
export interface AddRoleParams {
    /* */
    id?: number;

    /* */
    roleName?: string;

    /* */
    roleDescription?: string;
}

// Response interface
export interface AddRoleRes {
    /* */
    code: number;

    /* */
    data: Record<string, unknown>;

    /* */
    message: string;

    /* */
    description: string;
}

/**
 * addRole
 * @param {object} params Role
 * @param {number} params.id
 * @param {string} params.roleName
 * @param {string} params.roleDescription
 * @returns
 */
export function addRole(params: AddRoleParams): Promise<AddRoleRes> {
    return request.post(`/api/role/add`, params).then(
        (res: { data: AddRoleRes }) => res.data
    );
}

// Response interface
export interface DeleteRoleRes {
    /* */
    code: number;

    /* */
    data: Record<string, unknown>;

    /* */
    message: string;

    /* */
    description: string;
}

/**
 * deleteRole
 * @param {string} id
 * @returns
 */
export function deleteRole(id: number): Promise<DeleteRoleRes> {
    return request.delete(`/api/role/delete/${id}`).then(
        (res: { data: DeleteRoleRes }) => res.data
    );
}

// Response interface
export interface ListRes {
    /* */
    code: number;

    /* */
    data: {
        /* */
        id: number;

        /* */
        roleName: string;

        /* */
        roleDescription: string;
    }[];

    /* */
    message: string;

    /* */
    description: string;
}

/**
 * list
 * @returns
 */
export function list(): Promise<ListRes> {
    return request.get(`/api/role/list`).then(
        (res: { data: ListRes }) => res.data
    );
}

// Response interface
export interface QueryRoleAndUserCountRes {
    /* */
    code: number;

    /* */
    data: Record<string, unknown>;

    /* */
    message: string;

    /* */
    description: string;
}

/**
 * queryRoleAndUserCount
 * @returns
 */
export function queryRoleAndUserCount(): Promise<QueryRoleAndUserCountRes> {
    return request.get(`/api/role/select`).then(
        (res: { data: QueryRoleAndUserCountRes }) => res.data
    );
}

// Parameter interface
export interface UpdateRoleParams {
    /* */
    id?: number;

    /* */
    roleName?: string;

    /* */
    roleDescription?: string;
}

// Response interface
export interface UpdateRoleRes {
    /* */
    code: number;

    /* */
    data: Record<string, unknown>;

    /* */
    message: string;

    /* */
    description: string;
}

/**
 * updateRole
 * @param {object} params Role
 * @param {number} params.id
 * @param {string} params.roleName
 * @param {string} params.roleDescription
 * @returns
 */
export function updateRole(params: UpdateRoleParams): Promise<UpdateRoleRes> {
    return request.put(`/api/role/update`, params).then(
        (res: { data: UpdateRoleRes }) => res.data
    );
}
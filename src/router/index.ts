import {createRouter, createWebHistory} from 'vue-router'
import UserHomeView from '../views/UserHomeView.vue'
import CarDetail from '../components/car/CarDetail.vue'
import OrderDetail from '../components/order/OrderDetail.vue'
import Dashboard from '../components/admin/Dashboard.vue'
import CarManagement from '../components/admin/CarManagement.vue'
import OrderManagement from '../components/admin/OrderManagement.vue'
import UserManagement from '../components/admin/UserManagement.vue'
import Profile from '../components/admin/Profile.vue'
import System from '../components/admin/System.vue'
import RegisterView from '../views/RegisterView.vue'
import LoginView from '../views/LoginView.vue'
import AdminHomeView from '../views/AdminHomeView.vue';

const router = createRouter({
    history: createWebHistory(import.meta.env.BASE_URL),
    routes: [
        {
            path: '/',
            redirect: '/login'
        },
        {
            path: '/login',
            name: 'login',
            component: LoginView,
        },
        {
            path: '/register',
            name: 'register',
            component: RegisterView,
        },
        {
            path: '/user',
            component: UserHomeView,
            children: [
                {
                    path: '',
                    redirect: '/user/home'
                },
                {
                    path: 'home',
                    name: 'user-home',
                    component: () => import('../components/user/UserHomeMain.vue'),
                },
                {
                    path: 'orders',
                    name: 'user-orders',
                    component: () => import('../components/user/UserOrderMain.vue'),
                }
            ]
        },
        {
            path: '/admin',
            component: AdminHomeView,
            children: [
                {
                    path: '',
                    redirect: '/admin/dashboard'
                },
                {
                    path: 'dashboard',
                    name: 'admin-dashboard',
                    component: Dashboard,
                },
                {
                    path: 'cars',
                    name: 'admin-cars',
                    component: CarManagement,
                },
                {
                    path: 'orders',
                    name: 'admin-orders',
                    component: OrderManagement,
                },
                {
                    path: 'users',
                    name: 'admin-users',
                    component: UserManagement,
                },
                {
                    path: 'profile',
                    name: 'admin-profile',
                    component: Profile,
                },
                {
                    path: 'settings',
                    name: 'admin-settings',
                    component: System,
                }
            ]
        },

        {
            path: '/car/:id',
            name: 'car-detail',
            component: CarDetail,
            props: true,
        },
        {
            path: '/orders',
            redirect: '/user/orders'
        },
        {
            path: '/order/:id',
            name: 'order-detail',
            component: OrderDetail,
            props: true,
        },

    ],
})

export default router

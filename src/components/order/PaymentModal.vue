<template>
  <el-dialog
    v-model="visible"
    title="订单支付"
    width="500px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="payment-modal"
  >
    <div class="payment-content">
      <!-- 订单信息 -->
      <div class="order-info">
        <div class="order-header">
          <h3>订单信息</h3>
          <span class="order-number">{{ orderInfo.orderNumber }}</span>
        </div>
        <div class="order-details">
          <div class="detail-item">
            <span class="label">车辆名称：</span>
            <span class="value">{{ orderInfo.carName }}</span>
          </div>
          <div class="detail-item">
            <span class="label">租赁日期：</span>
            <span class="value">{{ orderInfo.startDate }} 至 {{ orderInfo.endDate }}</span>
          </div>
          <div class="detail-item">
            <span class="label">租赁天数：</span>
            <span class="value">{{ orderInfo.totalDays }}天</span>
          </div>
        </div>
      </div>

      <!-- 支付金额 -->
      <div class="payment-amount">
        <div class="amount-label">支付金额</div>
        <div class="amount-value">¥{{ orderInfo.totalAmount }}</div>
      </div>

      <!-- 支付方式选择 -->
      <div class="payment-methods" v-if="!isProcessing">
        <h4>选择支付方式</h4>
        <div class="method-list">
          <div
            v-for="method in paymentMethods"
            :key="method.id"
            :class="['method-item', { active: selectedMethod === method.id }]"
            @click="selectMethod(method.id)"
          >
            <div class="method-icon">
              <el-icon :size="24">
                <component :is="method.icon" />
              </el-icon>
            </div>
            <div class="method-info">
              <div class="method-name">{{ method.name }}</div>
              <div class="method-desc">{{ method.description }}</div>
            </div>
            <div class="method-radio">
              <el-radio :model-value="selectedMethod === method.id" />
            </div>
          </div>
        </div>
      </div>

      <!-- 支付处理中 -->
      <div class="payment-processing" v-if="isProcessing">
        <div class="processing-icon">
          <el-icon :size="48" class="rotating">
            <Loading />
          </el-icon>
        </div>
        <div class="processing-text">
          <h4>支付处理中...</h4>
          <p>{{ processingText }}</p>
        </div>
        <div class="processing-progress">
          <el-progress 
            :percentage="processingProgress" 
            :show-text="false"
            stroke-width="6"
            color="#409EFF"
          />
          <span class="progress-text">{{ processingProgress }}%</span>
        </div>
      </div>

      <!-- 支付成功 -->
      <div class="payment-success" v-if="isSuccess">
        <div class="success-icon">
          <el-icon :size="64" color="#67C23A">
            <CircleCheckFilled />
          </el-icon>
        </div>
        <div class="success-text">
          <h4>支付成功！</h4>
          <p>您的订单已支付完成，请查收支付凭证。</p>
        </div>
        <div class="success-details">
          <div class="detail-row">
            <span class="label">交易金额：</span>
            <span class="value">¥{{ orderInfo.totalAmount }}</span>
          </div>
          <div class="detail-row">
            <span class="label">支付方式：</span>
            <span class="value">{{ getSelectedMethodName() }}</span>
          </div>
          <div class="detail-row">
            <span class="label">交易时间：</span>
            <span class="value">{{ formatDateTime(new Date()) }}</span>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button 
          v-if="!isProcessing && !isSuccess" 
          @click="handleCancel"
          :disabled="isProcessing"
        >
          取消
        </el-button>
        <el-button 
          v-if="!isProcessing && !isSuccess"
          type="primary" 
          @click="handleConfirmPayment"
          :disabled="!selectedMethod || isProcessing"
          :loading="isProcessing"
        >
          确认支付
        </el-button>
        <el-button 
          v-if="isSuccess"
          type="primary" 
          @click="handleClose"
        >
          完成
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { 
  CreditCard, 
  Wallet, 
  Loading, 
  CircleCheckFilled,
  Money,
  ChatDotRound
} from '@element-plus/icons-vue'

// Props
interface Props {
  modelValue: boolean
  orderInfo: {
    id: number
    orderNumber: string
    carName: string
    startDate: string
    endDate: string
    totalDays: number
    totalAmount: number
  }
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  orderInfo: () => ({
    id: 0,
    orderNumber: '',
    carName: '',
    startDate: '',
    endDate: '',
    totalDays: 0,
    totalAmount: 0
  })
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'payment-success': [orderId: number, paymentInfo: any]
  'payment-cancel': []
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const selectedMethod = ref('')
const isProcessing = ref(false)
const isSuccess = ref(false)
const processingProgress = ref(0)
const processingText = ref('正在连接支付网关...')

// 支付方式
const paymentMethods = ref([
  {
    id: 'alipay',
    name: '支付宝',
    description: '推荐使用，安全快捷',
    icon: 'Money'
  },
  {
    id: 'wechat',
    name: '微信支付',
    description: '扫码支付，即时到账',
    icon: 'ChatDotRound'
  },
  {
    id: 'card',
    name: '银行卡',
    description: '支持各大银行储蓄卡和信用卡',
    icon: 'CreditCard'
  },
  {
    id: 'wallet',
    name: '余额支付',
    description: '使用账户余额支付',
    icon: 'Wallet'
  }
])

// 选择支付方式
const selectMethod = (methodId: string) => {
  selectedMethod.value = methodId
}

// 获取选中的支付方式名称
const getSelectedMethodName = () => {
  const method = paymentMethods.value.find(m => m.id === selectedMethod.value)
  return method ? method.name : ''
}

// 格式化日期时间
const formatDateTime = (date: Date) => {
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 模拟支付处理
const simulatePayment = async () => {
  isProcessing.value = true
  processingProgress.value = 0
  processingText.value = '正在连接支付网关...'

  // 模拟支付进度
  const steps = [
    { progress: 20, text: '正在验证订单信息...' },
    { progress: 40, text: '正在连接支付网关...' },
    { progress: 60, text: '正在处理支付请求...' },
    { progress: 80, text: '正在确认支付结果...' },
    { progress: 100, text: '支付处理完成...' }
  ]

  for (const step of steps) {
    await new Promise(resolve => setTimeout(resolve, 800))
    processingProgress.value = step.progress
    processingText.value = step.text
  }

  // 支付完成
  await new Promise(resolve => setTimeout(resolve, 500))
  isProcessing.value = false
  isSuccess.value = true
}

// 确认支付
const handleConfirmPayment = async () => {
  if (!selectedMethod.value) {
    return
  }

  await simulatePayment()

  // 发送支付成功事件
  const paymentInfo = {
    method: selectedMethod.value,
    methodName: getSelectedMethodName(),
    amount: props.orderInfo.totalAmount,
    paymentTime: new Date(),
    transactionId: `TXN${Date.now()}${Math.random().toString(36).substr(2, 6).toUpperCase()}`
  }

  emit('payment-success', props.orderInfo.id, paymentInfo)
}

// 取消支付
const handleCancel = () => {
  emit('payment-cancel')
  visible.value = false
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
  // 重置状态
  selectedMethod.value = ''
  isProcessing.value = false
  isSuccess.value = false
  processingProgress.value = 0
}

// 监听弹窗关闭，重置状态
watch(visible, (newVal) => {
  if (!newVal) {
    selectedMethod.value = ''
    isProcessing.value = false
    isSuccess.value = false
    processingProgress.value = 0
  }
})
</script>

<style scoped>
.payment-modal {
  border-radius: 12px;
}

.payment-content {
  padding: 20px 0;
}

/* 订单信息 */
.order-info {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.order-header h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.order-number {
  font-size: 14px;
  color: #909399;
  font-family: monospace;
}

.order-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-item .label {
  font-size: 14px;
  color: #606266;
}

.detail-item .value {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
}

/* 支付金额 */
.payment-amount {
  text-align: center;
  margin-bottom: 24px;
  padding: 20px;
  background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
  border-radius: 12px;
  color: white;
}

.amount-label {
  font-size: 16px;
  margin-bottom: 8px;
  opacity: 0.9;
}

.amount-value {
  font-size: 32px;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 支付方式 */
.payment-methods h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #303133;
}

.method-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.method-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border: 2px solid #E4E7ED;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.method-item:hover {
  border-color: #409EFF;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.method-item.active {
  border-color: #409EFF;
  background: #f0f9ff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
}

.method-icon {
  margin-right: 12px;
  color: #409EFF;
}

.method-info {
  flex: 1;
}

.method-name {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.method-desc {
  font-size: 12px;
  color: #909399;
}

.method-radio {
  margin-left: 12px;
}

/* 支付处理中 */
.payment-processing {
  text-align: center;
  padding: 40px 20px;
}

.processing-icon {
  margin-bottom: 20px;
}

.rotating {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.processing-text h4 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #303133;
}

.processing-text p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.processing-progress {
  margin-top: 24px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.progress-text {
  font-size: 14px;
  color: #409EFF;
  font-weight: 600;
  min-width: 40px;
}

/* 支付成功 */
.payment-success {
  text-align: center;
  padding: 40px 20px;
}

.success-icon {
  margin-bottom: 20px;
}

.success-text h4 {
  margin: 0 0 8px 0;
  font-size: 20px;
  color: #67C23A;
}

.success-text p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.success-details {
  margin-top: 24px;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  text-align: left;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.detail-row .label {
  font-size: 14px;
  color: #606266;
}

.detail-row .value {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
}

/* 对话框底部 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .payment-modal {
    width: 95% !important;
  }
  
  .method-item {
    padding: 12px;
  }
  
  .method-name {
    font-size: 14px;
  }
  
  .method-desc {
    font-size: 11px;
  }
  
  .amount-value {
    font-size: 28px;
  }
}
</style> 
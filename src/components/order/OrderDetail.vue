<template>
  <div class="order-detail-page">
    <div class="container">
      <!-- 返回按钮 -->
      <div class="back-section">
        <el-button
            type="text"
            @click="goBack"
            class="back-btn"
        >
          <el-icon>
            <ArrowLeft/>
          </el-icon>
          返回
        </el-button>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-section">
        <el-skeleton :rows="10" animated/>
      </div>

      <!-- 订单详情 -->
      <div v-else-if="order" class="order-detail">
        <!-- 订单头部 -->
        <div class="order-header">
          <div class="order-info">
            <h1 class="order-title">订单详情</h1>
            <p class="order-id">订单号：{{ order.orderNumber }}</p>
          </div>
          <div class="order-status">
            <el-tag
                :type="(ORDER_STATUS_MAP[order.status as keyof typeof ORDER_STATUS_MAP] || { type: 'info' }).type as any"
                size="large">
              {{ (ORDER_STATUS_MAP[order.status as keyof typeof ORDER_STATUS_MAP] || {text: order.statusText}).text }}
            </el-tag>
          </div>
        </div>

        <!-- 汽车信息 -->
        <div class="car-info-section">
          <h3 class="section-title">租赁车辆</h3>
          <div class="car-card">
            <div class="car-image">
              <el-image :src="carImage"/>
            </div>
            <div class="car-details">
              <h4 class="car-name">{{ order.carName }}</h4>
              <p class="car-desc">优质车辆，安全可靠</p>
            </div>
          </div>
        </div>

        <!-- 租赁信息 -->
        <div class="rental-info-section">
          <h3 class="section-title">租赁信息</h3>
          <div class="info-grid">
            <div class="info-item rental-date-item">
              <span class="label">租赁日期</span>
              <span class="value rental-date-value">{{ order.startDate }} 至 {{ order.endDate }}</span>
            </div>
            <div class="info-item days-item">
              <span class="label">租赁天数</span>
              <span class="value">{{ order.totalDays }}天</span>
            </div>
            <div class="info-item daily-rate-item">
              <span class="label">日租金</span>
              <span class="value">¥{{ Math.round(order.totalAmount / order.totalDays) }}</span>
            </div>
            <div class="info-item total-item">
              <span class="label">总费用</span>
              <span class="value total-price">¥{{ order.totalAmount }}</span>
            </div>
          </div>
        </div>

        <!-- 订单时间 -->
        <div class="time-info-section">
          <h3 class="section-title">订单时间</h3>
          <div class="time-grid">
            <div class="time-item">
              <span class="label">创建时间</span>
              <span class="value">{{ formatDate(order.createdAt) }}</span>
            </div>
            <div class="time-item">
              <span class="label">更新时间</span>
              <span class="value">{{ formatDate(order.updatedAt) }}</span>
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="actions-section">
          <div class="action-buttons">
            <el-button
                v-if="order.status === 'pending'"
                type="success"
                size="large"
                @click="showPaymentModal = true"
                :loading="paying"
            >
              立即支付
            </el-button>

            <el-button
                v-if="order.status === 'pending'"
                type="danger"
                size="large"
                @click="handleCancelOrder"
                :loading="cancelling"
            >
              取消订单
            </el-button>

            <el-button
                type="primary"
                size="large"
                @click="$router.push('/user/orders')"
            >
              返回订单列表
            </el-button>
          </div>
        </div>

        <!-- 支付弹窗 -->
        <PaymentModal
            v-model="showPaymentModal"
            :order-info="order"
            @payment-success="handlePaymentSuccess"
            @payment-cancel="handlePaymentCancel"
        />

        <!-- 支付状态检查对话框 -->
        <el-dialog
            v-model="showPaymentStatusDialog"
            title="支付状态检查"
            width="400px"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            :show-close="true"
            @close="handleClosePaymentStatusDialog"
        >
          <div class="payment-status-content">
            <div class="status-icon">
              <el-icon class="is-loading" size="40">
                <Loading />
              </el-icon>
            </div>
            <p class="status-text">{{ paymentStatusText }}</p>
            <p class="status-progress">
              检查进度: {{ paymentCheckAttempts }} / {{ maxPaymentAttempts }}
            </p>
            <p class="status-tip">请不要关闭此页面，正在检查您的支付状态...</p>
          </div>

          <template #footer>
            <div class="dialog-footer">
              <el-button @click="handleClosePaymentStatusDialog">
                取消检查
              </el-button>
            </div>
          </template>
        </el-dialog>
      </div>

      <!-- 错误状态 -->
      <div v-else class="error-section">
        <el-empty description="订单不存在">
          <el-button type="primary" @click="$router.push('/user/orders')">
            返回订单列表
          </el-button>
        </el-empty>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {ref, computed, onMounted} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {ElMessage, ElMessageBox} from 'element-plus'
import {ArrowLeft, Loading} from '@element-plus/icons-vue'
import {
  queryOrdersPage,
  type QueryOrdersPageParams,
  payOrders,
  cancelOrders
} from '@/services/ordersApi.ts'
import {transformOrderData} from '@/config/api.ts'
import PaymentModal from './PaymentModal.vue'
import {getCarById} from "@/services/carApi.ts";
import axios from "axios";

// 订单接口类型定义
interface Order {
  id: number
  carId: number
  carName: string
  userId: number
  orderNumber: string
  startDate: string
  endDate: string
  totalDays: number
  dailyRate: number
  totalAmount: number
  status: string
  statusText: string
  paymentMethod?: string
  paymentTime?: Date | null
  pickupLocation?: string
  returnLocation?: string
  contactPhone: string
  remark?: string
  createdAt: string
  updatedAt: string
  isDelete: number
}

// 订单状态映射
const ORDER_STATUS_MAP = {
  pending: {type: 'warning', text: '待支付'},
  paid: {type: 'info', text: '已支付'},
  completed: {type: 'success', text: '已完成'},
  cancelled: {type: 'danger', text: '已取消'}
} as const

// 路由参数
const route = useRoute()
const router = useRouter()
const orderId = computed(() => Number(route.params.id))

// 响应式数据
const order = ref<Order | null>(null)
const loading = ref(true)
const paying = ref(false)
const cancelling = ref(false)
const showPaymentModal = ref(false)
const carImage = ref()
const showPaymentStatusDialog = ref(false)
const paymentStatusText = ref('')
const paymentCheckAttempts = ref(0)
const maxPaymentAttempts = ref(60)

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 获取订单详情
const fetchOrderDetail = async () => {
  loading.value = true
  try {
    const params: QueryOrdersPageParams = {
      pageNum: 1,
      pageSize: 1,
      params: {
        id: orderId.value,
        isDelete: 0
      }
    }

    const response = await queryOrdersPage(params)

    if (response.code === 0 && response.data && response.data.list.length > 0) {
      order.value = transformOrderData(response.data.list[0])
      const carId = order.value.carId;
      const carResponse = await getCarById(carId);
      const car = carResponse.data;
      carImage.value = car.image;
    } else {
      ElMessage.error('订单不存在')
      order.value = null
    }
  } catch (error) {
    ElMessage.error('获取订单详情失败')
    console.error('获取订单详情错误:', error)
    order.value = null
  } finally {
    loading.value = false
  }
}

// 取消订单
const handleCancelOrder = async () => {
  if (!order.value) return

  try {
    cancelling.value = true

    await ElMessageBox.confirm(
        '确定要取消这个订单吗？取消后无法恢复。',
        '确认取消',
        {
          confirmButtonText: '确定取消',
          cancelButtonText: '取消',
          type: 'warning'
        }
    )

    const response = await cancelOrders({
      id: order.value.id,
      status: 'cancelled'
    })

    if (response.code === 200) {
      ElMessage.success('订单已取消')
      // 刷新订单详情
      await fetchOrderDetail()
    } else {
      ElMessage.error(response.message || '取消订单失败')
    }

  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('取消订单失败，请重试')
      console.error('取消订单错误:', error)
    }
  } finally {
    cancelling.value = false
  }
}

// 处理支付成功
const handlePaymentSuccess = async (orderId: number, paymentInfo: any) => {
  try {
    paying.value = true

    // 创建符合后端格式的日期字符串
    const now = new Date()
    const year = now.getFullYear()
    const month = String(now.getMonth() + 1).padStart(2, '0')
    const day = String(now.getDate()).padStart(2, '0')
    const hours = String(now.getHours()).padStart(2, '0')
    const minutes = String(now.getMinutes()).padStart(2, '0')
    const seconds = String(now.getSeconds()).padStart(2, '0')
    const formattedDate = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`

    if (paymentInfo.methodName === '支付宝') {
      try {
        // 1. 获取支付页面
        const payResponse = await axios.get(`${import.meta.env.VITE_API_URL}/api/alipay/pay`, {
          params: {
            orderNo: orderId,
            amount: order.value?.totalAmount || '',
            subject: order.value?.carName || '',
          }
        });

        // 2. 打开支付页面
        let newWindow = window.open('', '_blank');
        if (newWindow) {
          newWindow.document.write(payResponse.data);
          newWindow.document.close();
        } else {
          ElMessage.error('无法打开支付页面，请检查浏览器弹窗设置');
          return;
        }

        // 3. 显示支付等待提示
        ElMessage.info('请在新窗口完成支付，支付完成后将自动更新订单状态');

        // 4. 显示支付状态检查对话框
        showPaymentStatusDialog.value = true;
        paymentStatusText.value = '等待支付完成...';
        paymentCheckAttempts.value = 0;

        // 5. 开始轮询检查支付状态
        await checkPaymentStatus(orderId, formattedDate, paymentInfo.methodName);

      } catch (error) {
        console.error('支付宝支付错误:', error);
        ElMessage.error('支付过程中出现错误，请重试');
      }
    } else {
      //不是支付宝
      const response = await payOrders({
        id: orderId,
        carId: order.value?.carId || 0,
        status: 'paid',
        paymentTime: formattedDate,
        paymentMethod: paymentInfo.methodName
      })

      if (response.code === 0) {
        ElMessage.success('支付成功！')
        // 刷新订单详情
        await fetchOrderDetail()
        // 关闭支付弹窗
        showPaymentModal.value = false
      } else {
        ElMessage.error(response.message || '支付失败')
      }
    }
  } catch (error) {
    ElMessage.error('支付失败，请重试')
    console.error('支付错误:', error)
  } finally {
    paying.value = false
  }
}

// 轮询检查支付状态
const checkPaymentStatus = async (orderId: number, formattedDate: string, paymentMethod: string) => {
  const maxAttempts = 60; // 最多检查60次，每次间隔5秒，总共5分钟
  const interval = 5000; // 5秒间隔
  maxPaymentAttempts.value = maxAttempts;

  for (let attempts = 1; attempts <= maxAttempts; attempts++) {
    try {
      paymentCheckAttempts.value = attempts;
      paymentStatusText.value = `正在检查支付状态... (${attempts}/${maxAttempts})`;

      // 调用后端接口检查订单支付状态
      const statusResponse = await queryOrdersPage({
        pageNum: 1,
        pageSize: 1,
        params: {
          id: orderId,
          isDelete: 0
        }
      });

      // 检查订单状态是否已更新为已支付
      if (statusResponse.code === 0 && statusResponse.data && statusResponse.data.list.length > 0) {
        const currentOrder = transformOrderData(statusResponse.data.list[0]);
        if (currentOrder.status === 'paid') {
          // 订单状态已更新为已支付，说明支付成功
          paymentStatusText.value = '支付成功！';
          showPaymentStatusDialog.value = false;
          ElMessage.success('支付成功！');
          // 刷新订单详情
          await fetchOrderDetail();
          // 关闭支付弹窗
          showPaymentModal.value = false;
          return true;
        }
      }

      // 支付状态未确定，等待后继续轮询
      if (attempts < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, interval));
      }

    } catch (error) {
      console.error('检查支付状态错误:', error);
      if (attempts >= maxAttempts) {
        showPaymentStatusDialog.value = false;
        ElMessage.error('支付状态检查失败，请手动刷新页面查看订单状态');
        return false;
      }

      // 出错时等待后继续轮询
      if (attempts < maxAttempts) {
        await new Promise(resolve => setTimeout(resolve, interval));
      }
    }
  }

  // 超时未获取到明确状态
  showPaymentStatusDialog.value = false;
  ElMessage.warning('支付状态检查超时，请手动刷新页面查看订单状态');
  return false;
};

// 关闭支付状态检查对话框
const handleClosePaymentStatusDialog = () => {
  showPaymentStatusDialog.value = false;
  ElMessage.warning('已取消支付状态检查，如已完成支付请手动刷新页面');
};

// 处理支付取消
const handlePaymentCancel = () => {
  ElMessage.info('支付已取消')
}

// 返回订单列表
const goBack = () => {
  router.go(-1)
}

// 组件挂载时获取订单详情
onMounted(() => {
  if (orderId.value) {
    fetchOrderDetail()
  } else {
    ElMessage.error('订单ID无效')
    router.push('/user/orders')
  }
})
</script>

<style scoped>
.order-detail-page {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding: 40px 0;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 20px;
}

.back-section {
  margin-bottom: 24px;
}

.back-btn {
  font-size: 16px;
  color: #409EFF;
}

.back-btn:hover {
  color: #66b1ff;
}

.loading-section {
  background: white;
  border-radius: 12px;
  padding: 32px;
}

.order-detail {
  background: white;
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid #EBEEF5;
}

.order-title {
  font-family: 'Poppins', sans-serif;
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  margin: 0 0 8px 0;
}

.order-id {
  font-size: 16px;
  color: #909399;
  margin: 0;
}

.section-title {
  font-family: 'Poppins', sans-serif;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 16px 0;
}

.car-info-section {
  margin-bottom: 32px;
}

.car-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: #f5f7fa;
  border-radius: 8px;
}

.car-image {
  width: 100px;
  height: 100px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.car-details {
  flex: 1;
}

.car-name {
  font-family: 'Poppins', sans-serif;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 4px 0;
}

.car-desc {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

.rental-info-section {
  margin-bottom: 32px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  grid-template-areas: 
    "rental-date rental-date"
    "days daily-rate"
    "total total";
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #f5f7fa;
  border-radius: 8px;
  min-height: 60px;
}

.label {
  font-size: 14px;
  color: #606266;
  white-space: nowrap;
  margin-right: 12px;
}

.value {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
  text-align: right;
  word-break: keep-all;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 60%;
}

.total-price {
  font-size: 18px;
  font-weight: 600;
  color: #FF6700;
}

/* 租赁日期特殊样式 */
.rental-date-item {
  grid-area: rental-date;
}

.rental-date-value {
  max-width: 70% !important;
  font-size: 13px !important;
}

.days-item {
  grid-area: days;
}

.daily-rate-item {
  grid-area: daily-rate;
}

.total-item {
  grid-area: total;
}

.time-info-section {
  margin-bottom: 32px;
}

.time-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.time-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #f5f7fa;
  border-radius: 8px;
}

.actions-section {
  padding-top: 24px;
  border-top: 1px solid #EBEEF5;
}

.action-buttons {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.error-section {
  background: white;
  border-radius: 12px;
  padding: 60px 32px;
  text-align: center;
}

/* 支付状态对话框样式 */
.payment-status-content {
  text-align: center;
  padding: 20px 0;
}

.status-icon {
  margin-bottom: 16px;
}

.status-text {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin: 16px 0 8px 0;
}

.status-progress {
  font-size: 14px;
  color: #909399;
  margin: 8px 0;
}

.status-tip {
  font-size: 12px;
  color: #C0C4CC;
  margin: 16px 0 0 0;
}

</style> 
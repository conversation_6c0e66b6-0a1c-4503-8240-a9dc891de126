<template>
  <div class="order-list-page">
    <div class="container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1 class="page-title">我的订单</h1>
        <p class="page-subtitle">查看您的所有租赁订单</p>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-section">
        <el-skeleton :rows="5" animated />
      </div>

      <!-- 订单列表 -->
      <div v-else-if="orders.length > 0" class="orders-section">
        <!-- 筛选栏 -->
        <div class="filter-bar">
          <el-select 
            v-model="statusFilter" 
            placeholder="筛选状态"
            clearable
            style="width: 200px"
          >
            <el-option label="全部" value="" />
            <el-option label="待支付" value="pending" />
            <el-option label="已支付" value="paid" />
            <el-option label="已完成" value="completed" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </div>

        <!-- 订单卡片列表 -->
        <div class="orders-grid">
          <div 
            v-for="order in filteredOrders" 
            :key="order.id"
            class="order-card"
            @click="viewOrderDetail(order.id)"
          >
            <div class="order-header">
              <div class="order-info">
                <h3 class="order-title">{{ order.carName }}</h3>
                <p class="order-id">订单号：{{ order.id }}</p>
              </div>
                          <el-tag :type="(ORDER_STATUS_MAP[order.status as keyof typeof ORDER_STATUS_MAP] || { type: 'info' }).type as any">
              {{ (ORDER_STATUS_MAP[order.status as keyof typeof ORDER_STATUS_MAP] || { text: order.statusText }).text }}
            </el-tag>
            </div>

            <div class="order-details">
              <div class="detail-item">
                <span class="label">租赁日期：</span>
                <span class="value">{{ order.startDate }} 至 {{ order.endDate }}</span>
              </div>
              <div class="detail-item">
                <span class="label">租赁天数：</span>
                <span class="value">{{ order.totalDays }}天</span>
              </div>
              <div class="detail-item">
                <span class="label">总费用：</span>
                <span class="value price">¥{{ order.totalAmount }}</span>
              </div>
              <div class="detail-item">
                <span class="label">创建时间：</span>
                <span class="value">{{ formatDate(order.createdAt) }}</span>
              </div>
            </div>

            <div class="order-actions">
              <el-button 
                type="primary" 
                size="small"
                @click.stop="viewOrderDetail(order.id)"
              >
                查看详情
              </el-button>
              
              <el-button 
                v-if="order.status === 'pending'"
                type="success" 
                size="small"
                @click.stop="handlePayOrder(order.id)"
                :loading="payingOrderId === order.id"
              >
                立即支付
              </el-button>
              
              <el-button 
                v-if="order.status === 'pending'"
                type="danger" 
                size="small"
                @click.stop="handleCancelOrder(order.id)"
                :loading="cancellingOrderId === order.id"
              >
                取消订单
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-section">
        <el-empty description="暂无订单">
          <el-button type="primary" @click="$router.push('/')">
            去租车
          </el-button>
        </el-empty>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  queryOrdersPage,
  updateOrders,
  type QueryOrdersPageParams,
  payOrders,
  cancelOrders
} from '@/services/ordersApi.ts'
import { transformOrderData } from '@/config/api.ts'
import { getUserInfo } from '@/utils/auth.ts'

// 订单接口类型定义
interface Order {
  id: number
  carId: number
  carName: string
  userId: number
  orderNumber: string
  startDate: string
  endDate: string
  totalDays: number
  dailyRate: number
  totalAmount: number
  status: string
  statusText: string
  paymentMethod?: string
  paymentTime?: Date | null
  pickupLocation?: string
  returnLocation?: string
  contactPhone: string
  remark?: string
  createdAt: string
  updatedAt: string
  isDelete: number
}

// 订单状态映射
const ORDER_STATUS_MAP = {
  pending: {type: 'warning', text: '待支付'},
  paid: {type: 'info', text: '已支付'},
  completed: {type: 'success', text: '已完成'},
  cancelled: {type: 'danger', text: '已取消'}
} as const

const router = useRouter()

// 响应式数据
const orders = ref<Order[]>([])
const loading = ref(true)
const statusFilter = ref('')
const payingOrderId = ref<number | null>(null)
const cancellingOrderId = ref<number | null>(null)

// 计算属性
const filteredOrders = computed(() => {
  if (!statusFilter.value) {
    return orders.value
  }
  return orders.value.filter(order => order.status === statusFilter.value)
})

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 获取当前用户ID
const getCurrentUserId = (): number => {
  const userInfo = getUserInfo()
  return userInfo?.id || 0
}

// 获取订单列表
const fetchOrders = async () => {
  loading.value = true
  try {
    const userId = getCurrentUserId()
    if (!userId) {
      ElMessage.error('用户未登录')
      orders.value = []
      return
    }

    const params: QueryOrdersPageParams = {
      pageNum: 1,
      pageSize: 1000, // 获取所有订单
      params: {
        userId: userId,
        isDelete: 0
      }
    }
    const response = await queryOrdersPage(params)
    
    if (response.code === 0 && response.data) {
      orders.value = response.data.list.map(transformOrderData)
    } else {
      ElMessage.error(response.message || '获取订单列表失败')
      orders.value = []
    }
  } catch (error) {
    ElMessage.error('获取订单列表失败')
    console.error('获取订单列表错误:', error)
    orders.value = []
  } finally {
    loading.value = false
  }
}

// 查看订单详情
const viewOrderDetail = (orderId: number) => {
  router.push(`/order/${orderId}`)
}

// 支付订单
const handlePayOrder = async (orderId: number) => {
  try {
    payingOrderId.value = orderId
    
    await ElMessageBox.confirm(
      '确定要支付这个订单吗？',
      '确认支付',
      {
        confirmButtonText: '确定支付',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 创建符合后端格式的日期字符串
    const now = new Date()
    const year = now.getFullYear()
    const month = String(now.getMonth() + 1).padStart(2, '0')
    const day = String(now.getDate()).padStart(2, '0')
    const hours = String(now.getHours()).padStart(2, '0')
    const minutes = String(now.getMinutes()).padStart(2, '0')
    const seconds = String(now.getSeconds()).padStart(2, '0')
    const formattedDate = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    
    const response = await payOrders({
      id: orderId,
      status: 'paid',
      paymentTime: formattedDate,
      paymentMethod: '在线支付'
    })
    
    if (response.code === 0) {
      ElMessage.success('订单支付成功')
    } else {
      ElMessage.error(response.description || '支付失败')
    }
    
    // 刷新订单列表
    await fetchOrders()
    
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('支付失败，请重试')
      console.error('支付错误:', error)
    }
  } finally {
    payingOrderId.value = null
  }
}

// 取消订单
const handleCancelOrder = async (orderId: number) => {
  try {
    cancellingOrderId.value = orderId
    
    await ElMessageBox.confirm(
      '确定要取消这个订单吗？取消后无法恢复。',
      '确认取消',
      {
        confirmButtonText: '确定取消',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const response = await cancelOrders({
      id: orderId,
      status: 'cancelled'
    })
    
    if (response.code === 0) {
      ElMessage.success('订单取消成功')
    } else {
      ElMessage.error(response.message || '取消失败')
    }
    
    // 刷新订单列表
    await fetchOrders()
    
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('取消订单失败，请重试')
      console.error('取消订单错误:', error)
    }
  } finally {
    cancellingOrderId.value = null
  }
}

onMounted(() => {
  fetchOrders()
})
</script>

<style scoped>
.order-list-page {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding: 40px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
}

.page-title {
  font-family: 'Poppins', sans-serif;
  font-size: 36px;
  font-weight: 700;
  color: #303133;
  margin: 0 0 12px 0;
}

.page-subtitle {
  font-size: 16px;
  color: #909399;
  margin: 0;
}

.loading-section {
  background: white;
  border-radius: 12px;
  padding: 32px;
}

.filter-bar {
  margin-bottom: 24px;
  display: flex;
  justify-content: flex-end;
}

.orders-grid {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.order-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.order-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.order-title {
  font-family: 'Poppins', sans-serif;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 4px 0;
}

.order-id {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

.order-details {
  margin-bottom: 20px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.detail-item:last-child {
  border-bottom: none;
}

.label {
  font-size: 14px;
  color: #606266;
}

.value {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
}

.value.price {
  font-size: 16px;
  font-weight: 600;
  color: #FF6700;
}

.order-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.empty-section {
  background: white;
  border-radius: 12px;
  padding: 60px 32px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-title {
    font-size: 28px;
  }
  
  .order-card {
    padding: 20px;
  }
  
  .order-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .order-actions {
    flex-direction: column;
  }
  
  .order-actions .el-button {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 16px;
  }
  
  .page-title {
    font-size: 24px;
  }
  
  .order-card {
    padding: 16px;
  }
}
</style> 
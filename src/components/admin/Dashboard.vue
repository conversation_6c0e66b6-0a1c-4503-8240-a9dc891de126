<template>
  <div class="dashboard">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">数据概览</h1>
      <p class="page-subtitle">实时监控系统运营数据和关键指标</p>
    </div>

    <!-- KPI 指标卡片 -->
    <div class="kpi-section">
      <el-row :gutter="24">
        <el-col :span="6" v-for="kpi in kpiData" :key="kpi.id">
          <el-card class="kpi-card" @click="handleKpiClick(kpi)">
            <div class="kpi-content">
              <div class="kpi-icon" :style="{ backgroundColor: kpi.color }">
                <el-icon :size="24">
                  <component :is="kpi.icon" />
                </el-icon>
              </div>
              <div class="kpi-info">
                <div class="kpi-value">{{ kpi.value }}</div>
                <div class="kpi-label">{{ kpi.label }}</div>
                <div class="kpi-trend" v-if="kpi.trend">
                  <el-icon :color="kpi.trend > 0 ? '#67C23A' : '#F56C6C'">
                    <ArrowUp v-if="kpi.trend > 0" />
                    <ArrowDown v-else />
                  </el-icon>
                  <span :style="{ color: kpi.trend > 0 ? '#67C23A' : '#F56C6C' }">
                    {{ Math.abs(kpi.trend) }}%
                  </span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 数据可视化区域 -->
    <div class="charts-section">
      <el-row :gutter="24">
        <!-- 订单分析图表 -->
        <el-col :span="16">
          <el-card class="chart-card">
            <template #header>
              <div class="chart-header">
                <span>订单分析</span>
                <div class="chart-controls">
                  <el-radio-group v-model="timeRange" size="small">
                    <el-radio-button label="today">今日</el-radio-button>
                    <el-radio-button label="week">本周</el-radio-button>
                    <el-radio-button label="month">本月</el-radio-button>
                  </el-radio-group>
                  <el-button type="text" @click="refreshData">
                    <el-icon><Refresh /></el-icon>
                  </el-button>
                </div>
              </div>
            </template>
                         <div class="chart-container">
               <div id="orderChart" class="chart" style="height: 300px;"></div>
             </div>
          </el-card>
        </el-col>

        <!-- 订单状态分布 -->
        <el-col :span="8">
          <el-card class="chart-card">
            <template #header>
              <span>订单状态分布</span>
            </template>
                         <div class="chart-container">
               <div id="statusChart" class="chart" style="height: 300px;"></div>
             </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 车辆状态看板 -->
    <div class="vehicle-section">
      <el-card class="vehicle-card">
        <template #header>
          <span>车辆状态看板</span>
        </template>
        <el-row :gutter="16">
          <el-col :span="6" v-for="status in vehicleStatus" :key="status.type">
            <div class="status-card" :class="status.class">
              <div class="status-icon">
                <el-icon :size="32" :color="status.color">
                  <component :is="status.icon" />
                </el-icon>
              </div>
              <div class="status-info">
                <div class="status-count">{{ status.count }}</div>
                <div class="status-label">{{ status.label }}</div>
              </div>
              <el-button type="text" size="small" @click="viewVehicleDetails(status.type)">
                查看详情
              </el-button>
            </div>
          </el-col>
        </el-row>
      </el-card>
    </div>

    <!-- 动态信息区 -->
    <div class="info-section">
      <el-row :gutter="24">
        <!-- 最新订单 -->
        <el-col :span="12">
          <el-card class="info-card">
            <template #header>
              <span>最新订单</span>
            </template>
            <div class="order-list">
              <div 
                v-for="order in recentOrders" 
                :key="order.id" 
                class="order-item"
                :class="{ 'urgent': order.urgent }"
              >
                <div class="order-info">
                  <div class="order-id">订单 #{{ order.id }}</div>
                  <div class="order-customer">{{ order.customer }}</div>
                  <div class="order-time">{{ order.time }}</div>
                </div>
                <div class="order-status">
                  <el-tag :type="order.statusType" size="small">
                    {{ order.status }}
                  </el-tag>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 待办事项 -->
        <el-col :span="12">
          <el-card class="info-card">
            <template #header>
              <span>待办事项</span>
            </template>
            <div class="todo-list">
              <div 
                v-for="todo in todoList" 
                :key="todo.id" 
                class="todo-item"
              >
                <el-checkbox 
                  v-model="todo.completed" 
                  @change="handleTodoChange(todo)"
                >
                  {{ todo.title }}
                </el-checkbox>
                <div class="todo-time">{{ todo.time }}</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  ArrowUp, 
  ArrowDown, 
  Refresh,
  Document,
  Money,
  Van,
  Star,
  Warning,
  Clock,
  Check
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'

const router = useRouter()

// 时间范围
const timeRange = ref('month')

// KPI 数据
const kpiData = ref([
  {
    id: 1,
    label: '总订单量',
    value: '1,234',
    icon: 'Document',
    color: '#409EFF',
    trend: 12.5
  },
  {
    id: 2,
    label: '今日营收',
    value: '¥45,678',
    icon: 'Money',
    color: '#67C23A',
    trend: 8.3
  },
  {
    id: 3,
    label: '车辆利用率',
    value: '78%',
    icon: 'Van',
    color: '#E6A23C',
    trend: -2.1
  },
  {
    id: 4,
    label: '用户满意度',
    value: '4.8',
    icon: 'Star',
    color: '#F56C6C',
    trend: 5.2
  }
])

// 车辆状态数据
const vehicleStatus = ref([
  {
    type: 'available',
    label: '可租用',
    count: 45,
    icon: 'Van',
    color: '#67C23A',
    class: 'status-available'
  },
  {
    type: 'rented',
    label: '租用中',
    count: 32,
    icon: 'Clock',
    color: '#409EFF',
    class: 'status-rented'
  },
  {
    type: 'maintenance',
    label: '维护中',
    count: 8,
    icon: 'Warning',
    color: '#E6A23C',
    class: 'status-maintenance'
  },
  {
    type: 'offline',
    label: '已下线',
    count: 3,
    icon: 'Check',
    color: '#909399',
    class: 'status-offline'
  }
])

// 最新订单数据
const recentOrders = ref([
  {
    id: '2024001',
    customer: '张三',
    time: '2分钟前',
    status: '待支付',
    statusType: 'warning',
    urgent: true
  },
  {
    id: '2024002',
    customer: '李四',
    time: '5分钟前',
    status: '进行中',
    statusType: 'primary',
    urgent: false
  },
  {
    id: '2024003',
    customer: '王五',
    time: '10分钟前',
    status: '已完成',
    statusType: 'success',
    urgent: false
  },
  {
    id: '2024004',
    customer: '赵六',
    time: '15分钟前',
    status: '待支付',
    statusType: 'warning',
    urgent: true
  }
])

// 待办事项数据
const todoList = ref([
  {
    id: 1,
    title: '处理即将到期的订单',
    completed: false,
    time: '1小时内'
  },
  {
    id: 2,
    title: '车辆维护检查',
    completed: false,
    time: '2小时内'
  },
  {
    id: 3,
    title: '更新系统公告',
    completed: true,
    time: '已完成'
  },
  {
    id: 4,
    title: '审核新用户注册',
    completed: false,
    time: '4小时内'
  }
])

// 图表实例
let orderChart: echarts.ECharts | null = null
let statusChart: echarts.ECharts | null = null

// 定时器
let refreshTimer: number | null = null

// KPI 卡片点击
const handleKpiClick = (kpi: any) => {
  ElMessage.info(`点击了${kpi.label}`)
  // 这里可以跳转到对应的管理页面
}

// 刷新数据
const refreshData = () => {
  ElMessage.success('数据已刷新')
  initCharts()
}

// 查看车辆详情
const viewVehicleDetails = (type: string) => {
  router.push('/admin/cars')
  ElMessage.info(`查看${type}车辆详情`)
}

// 待办事项状态改变
const handleTodoChange = (todo: any) => {
  if (todo.completed) {
    ElMessage.success(`已完成: ${todo.title}`)
  }
}

// 初始化订单图表
const initOrderChart = () => {
  const chartDom = document.querySelector('#orderChart') as HTMLElement
  if (!chartDom) return

  orderChart = echarts.init(chartDom)
  
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['订单量', '营收']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '订单量',
        type: 'line',
        stack: 'Total',
        data: [120, 132, 101, 134, 90, 230, 210],
        smooth: true,
        lineStyle: {
          color: '#409EFF'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
              { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
            ]
          }
        }
      },
      {
        name: '营收',
        type: 'line',
        stack: 'Total',
        data: [220, 182, 191, 234, 290, 330, 310],
        smooth: true,
        lineStyle: {
          color: '#67C23A'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(103, 194, 58, 0.3)' },
              { offset: 1, color: 'rgba(103, 194, 58, 0.1)' }
            ]
          }
        }
      }
    ]
  }
  
  orderChart.setOption(option)
}

// 初始化状态图表
const initStatusChart = () => {
  const chartDom = document.querySelector('#statusChart') as HTMLElement
  if (!chartDom) return

  statusChart = echarts.init(chartDom)
  
  const option = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '订单状态',
        type: 'pie',
        radius: '50%',
        data: [
          { value: 1048, name: '进行中', itemStyle: { color: '#409EFF' } },
          { value: 735, name: '已完成', itemStyle: { color: '#67C23A' } },
          { value: 580, name: '待支付', itemStyle: { color: '#E6A23C' } },
          { value: 484, name: '已取消', itemStyle: { color: '#F56C6C' } }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  
  statusChart.setOption(option)
}

// 初始化图表
const initCharts = () => {
  nextTick(() => {
    initOrderChart()
    initStatusChart()
  })
}

// 开始定时刷新
const startRefreshTimer = () => {
  refreshTimer = window.setInterval(() => {
    refreshData()
  }, 5 * 60 * 1000) // 5分钟
}

// 停止定时刷新
const stopRefreshTimer = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
}

// 监听窗口大小变化
const handleResize = () => {
  if (orderChart) {
    orderChart.resize()
  }
  if (statusChart) {
    statusChart.resize()
  }
}

onMounted(() => {
  initCharts()
  startRefreshTimer()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  stopRefreshTimer()
  window.removeEventListener('resize', handleResize)
  if (orderChart) {
    orderChart.dispose()
  }
  if (statusChart) {
    statusChart.dispose()
  }
})
</script>

<style scoped>
.dashboard {
  padding: 24px;
}

.page-header {
  margin-bottom: 32px;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: #1f2d3d;
  margin: 0 0 8px 0;
}

.page-subtitle {
  color: #606266;
  margin: 0;
}

/* KPI 卡片样式 */
.kpi-section {
  margin-bottom: 32px;
}

.kpi-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.kpi-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.kpi-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.kpi-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.kpi-info {
  flex: 1;
}

.kpi-value {
  font-size: 24px;
  font-weight: 600;
  color: #1f2d3d;
  margin-bottom: 4px;
}

.kpi-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.kpi-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

/* 图表区域 */
.charts-section {
  margin-bottom: 32px;
}

.chart-card {
  height: 400px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.chart-container {
  height: 100%;
  padding: 16px;
}

.chart {
  width: 100%;
  height: 100%;
}

/* 车辆状态看板 */
.vehicle-section {
  margin-bottom: 32px;
}

.vehicle-card {
  height: 200px;
}

.status-card {
  height: 120px;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.status-card:hover {
  transform: translateY(-2px);
}

.status-available {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1px solid #67C23A;
}

.status-rented {
  background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
  border: 1px solid #409EFF;
}

.status-maintenance {
  background: linear-gradient(135deg, #fff7ed 0%, #ffedd5 100%);
  border: 1px solid #E6A23C;
}

.status-offline {
  background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
  border: 1px solid #909399;
}

.status-icon {
  margin-bottom: 8px;
}

.status-count {
  font-size: 24px;
  font-weight: 600;
  color: #1f2d3d;
  margin-bottom: 4px;
}

.status-label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

/* 信息区域 */
.info-section {
  margin-bottom: 32px;
}

.info-card {
  height: 300px;
}

.order-list, .todo-list {
  height: 220px;
  overflow-y: auto;
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.order-item:last-child {
  border-bottom: none;
}

.order-item.urgent {
  background: rgba(245, 108, 108, 0.1);
  border-radius: 4px;
  padding: 8px;
  margin: 4px 0;
}

.order-info {
  flex: 1;
}

.order-id {
  font-weight: 600;
  color: #1f2d3d;
  margin-bottom: 4px;
}

.order-customer {
  font-size: 14px;
  color: #606266;
  margin-bottom: 2px;
}

.order-time {
  font-size: 12px;
  color: #909399;
}

.todo-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.todo-item:last-child {
  border-bottom: none;
}

.todo-time {
  font-size: 12px;
  color: #909399;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard {
    padding: 16px;
  }
  
  .kpi-content {
    flex-direction: column;
    text-align: center;
  }
  
  .kpi-icon {
    width: 50px;
    height: 50px;
  }
  
  .status-card {
    height: 100px;
    padding: 12px;
  }
  
  .status-count {
    font-size: 20px;
  }
}
</style> 
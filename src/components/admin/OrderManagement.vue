<template>
  <div class="order-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">订单管理</h2>
        <p class="page-subtitle">管理所有租赁订单</p>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <el-row :gutter="16">
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon pending">
              <el-icon>
                <Clock/>
              </el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stats.pending }}</div>
              <div class="stat-label">待支付</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon paid">
              <el-icon>
                <Check/>
              </el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stats.paid }}</div>
              <div class="stat-label">已支付</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon completed">
              <el-icon>
                <Finished/>
              </el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stats.completed }}</div>
              <div class="stat-label">已完成</div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon cancelled">
              <el-icon>
                <Close/>
              </el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stats.cancelled }}</div>
              <div class="stat-label">已取消</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 筛选栏 -->
    <div class="filter-section">
      <el-row :gutter="16">
        <el-col :span="5">
          <el-input
              v-model="searchOrderOrCarQuery"
              placeholder="搜索订单号或汽车名称"
              clearable
              @input="handleSearch"
          >
            <template #prefix>
              <el-icon>
                <Search/>
              </el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="5">
          <el-input
              v-model="searchUserQuery"
              placeholder="搜索用户联系电话"
              clearable
              @input="handleSearch"
          >
            <template #prefix>
              <el-icon>
                <Search/>
              </el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-select v-model="statusFilter" placeholder="状态筛选" clearable @change="handleSearch">
            <el-option label="全部" value=""/>
            <el-option label="待支付" value="pending"/>
            <el-option label="已支付" value="paid"/>
            <el-option label="已完成" value="completed"/>
            <el-option label="已取消" value="cancelled"/>
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-date-picker
              v-model="dateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              @change="handleDateChange"
              style="width: 100%"
          />
        </el-col>
        <el-col :span="4">
          <el-button @click="handleRefresh">
            <el-icon>
              <Refresh/>
            </el-icon>
            刷新
          </el-button>
        </el-col>

      </el-row>
    </div>

    <!-- 订单列表 -->
    <div class="table-section">
      <el-table
          :data="orders"
          v-loading="loading"
          style="width: 100%"
          :row-class-name="getRowClassName"
      >
        <el-table-column prop="id" label="订单号" width="100"/>

        <el-table-column label="汽车信息" min-width="200">
          <template #default="{ row }">
            <div class="car-info">
              <div class="car-name">{{ row.carName }}</div>
              <div class="car-details">
                {{ row.startDate }} 至 {{ row.endDate }}
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="租赁信息" width="150">
          <template #default="{ row }">
            <div class="rental-info">
              <div>{{ row.totalDays }}天</div>
              <div class="price">¥{{ row.totalAmount }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="创建时间" width="180">
          <template #default="{ row }">
            {{ row.createdAt }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="280" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="handleViewDetail(row)">
              查看详情
            </el-button>
            <el-button
                v-if="row.status === 'pending'"
                size="small"
                type="success"
                @click="handleChangeStatus(row, 'paid')"
            >
              标记已支付
            </el-button>
            <el-button
                v-if="row.status === 'paid'"
                size="small"
                type="primary"
                @click="handleChangeStatus(row, 'completed')"
            >
              标记已完成
            </el-button>
            <el-button
                size="small"
                type="danger"
                @click="handleDeleteOrder(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[5, 10, 20, 50]"
            :total="totalOrders"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handlePageChange"
        />
      </div>
    </div>

    <!-- 订单详情对话框 -->
    <el-dialog
        v-model="showDetailDialog"
        title="订单详情"
        width="800px"
    >
      <div v-if="selectedOrder" class="order-detail">
        <!-- 基本信息 -->
        <div class="detail-section">
          <h3 class="section-title">基本信息</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="订单号">
              {{ selectedOrder.orderNumber || selectedOrder.id }}
            </el-descriptions-item>
            <el-descriptions-item label="订单状态">
              <el-tag :type="getStatusType(selectedOrder.status)">
                {{ getStatusText(selectedOrder.status) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="汽车ID">
              {{ selectedOrder.carId }}
            </el-descriptions-item>
            <el-descriptions-item label="汽车名称">
              {{ selectedOrder.carName }}
            </el-descriptions-item>
            <el-descriptions-item label="用户ID">
              {{ selectedOrder.userId }}
            </el-descriptions-item>
            <el-descriptions-item label="联系电话">
              {{ selectedOrder.contactPhone }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 租赁信息 -->
        <div class="detail-section">
          <h3 class="section-title">租赁信息</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="租赁开始日期">
              {{ selectedOrder.startDate }}
            </el-descriptions-item>
            <el-descriptions-item label="租赁结束日期">
              {{ selectedOrder.endDate }}
            </el-descriptions-item>
            <el-descriptions-item label="租赁天数">
              {{ selectedOrder.totalDays }}天
            </el-descriptions-item>
            <el-descriptions-item label="日租金">
              ¥{{ selectedOrder.dailyRate }}
            </el-descriptions-item>
            <el-descriptions-item label="总费用">
              <span class="price">¥{{ selectedOrder.totalAmount }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="取车地点">
              {{ selectedOrder.pickupLocation || '未指定' }}
            </el-descriptions-item>
            <el-descriptions-item label="还车地点">
              {{ selectedOrder.returnLocation || '未指定' }}
            </el-descriptions-item>
            <el-descriptions-item label="备注">
              {{ selectedOrder.remark || '无' }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 支付信息 -->
        <div class="detail-section">
          <h3 class="section-title">支付信息</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="支付方式">
              {{ selectedOrder.paymentMethod || '未知' }}
            </el-descriptions-item>
            <el-descriptions-item label="支付时间">
              {{ selectedOrder.paymentTime || '未知' }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 时间信息 -->
        <div class="detail-section">
          <h3 class="section-title">时间信息</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="创建时间">
              {{ selectedOrder.createdAt }}
            </el-descriptions-item>
            <el-descriptions-item label="更新时间">
              {{ selectedOrder.updatedAt }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import {ref, onMounted} from 'vue'
import {ElMessage, ElMessageBox} from 'element-plus'
import {
  Search,
  Refresh,
  Clock,
  Check,
  Finished,
  Close
} from '@element-plus/icons-vue'
import {
  queryOrdersPage,
  updateOrders,
  deleteOrders,
  type QueryOrdersPageParams,
  cancelOrders, payOrders, completeOrders
} from '@/services/ordersApi.ts'
import {transformOrderData, ORDER_STATUS_TEXT} from '@/config/api.ts'

// 订单接口类型定义
interface Order {
  id: number
  carId: number
  carName: string
  userId: number
  orderNumber: string
  startDate: string
  endDate: string
  totalDays: number
  dailyRate: number
  totalAmount: number
  status: string
  statusText: string
  paymentMethod?: string
  paymentTime?: string | null
  pickupLocation?: string
  returnLocation?: string
  contactPhone: string
  remark?: string
  createdAt: string
  updatedAt: string
  isDelete: number
}

// 订单状态映射
const ORDER_STATUS_MAP = {
  pending: {type: 'warning', text: '待支付'},
  paid: {type: 'info', text: '已支付'},
  completed: {type: 'success', text: '已完成'},
  cancelled: {type: 'danger', text: '已取消'}
} as const

// 响应式数据
const loading = ref(false)
const showDetailDialog = ref(false)
const selectedOrder = ref<Order | null>(null)
const searchOrderOrCarQuery = ref('')
const searchUserQuery = ref('')
const statusFilter = ref('')
const dateRange = ref<[Date, Date] | null>(null)
const currentPage = ref(1)
const pageSize = ref(5)
const totalOrders = ref(0)

// 统计数据
const stats = ref({
  pending: 0,
  paid: 0,
  completed: 0,
  cancelled: 0
})

// 订单数据
const orders = ref<Order[]>([])

// 获取状态类型
const getStatusType = (status: string) => {
  return ORDER_STATUS_MAP[status as keyof typeof ORDER_STATUS_MAP]?.type || 'info'
}

// 获取状态文本
const getStatusText = (status: string) => {
  return ORDER_STATUS_MAP[status as keyof typeof ORDER_STATUS_MAP]?.text || '未知'
}

// 获取行样式
const getRowClassName = ({row}: { row: Order }) => {
  switch (row.status) {
    case 'pending':
      return 'pending-row'
    case 'paid':
      return 'paid-row'
    case 'completed':
      return 'completed-row'
    case 'cancelled':
      return 'cancelled-row'
    default:
      return ''
  }
}

// 获取查询参数
const getQueryParams = (): QueryOrdersPageParams => {
  const params: QueryOrdersPageParams = {
    pageNum: currentPage.value,
    pageSize: pageSize.value,
    params: {
      isDelete: 0 // 只查询未删除的订单
    },
  }

  // 搜索条件
  if (searchOrderOrCarQuery.value.trim()) {
    // 如果是数字，可能是订单号
    if (/^\d+$/.test(searchOrderOrCarQuery.value.trim())) {
      params.params!.orderNumber = searchOrderOrCarQuery.value.trim()
    } else {
      // 否则按车辆名称搜索
      params.params!.carName = searchOrderOrCarQuery.value.trim()
    }
  }
  if (searchUserQuery.value.trim()) {
    params.params!.contactPhone = searchUserQuery.value.trim()
  }

  // 状态筛选
  if (statusFilter.value) {
    params.params!.status = statusFilter.value
  }

  // 日期筛选
  if (dateRange.value) {
    const [startDate, endDate] = dateRange.value
    params.params!.startDate = startDate
    params.params!.endDate = endDate
  }

  return params
}

// 获取订单列表
const fetchOrders = async () => {
  loading.value = true
  try {
    const params = getQueryParams()
    const response = await queryOrdersPage(params)

    if (response.code === 0 && response.data) {
      // 转换订单数据
      orders.value = response.data.list.map(transformOrderData)
      totalOrders.value = response.data.total

      // 计算统计数据
      stats.value = {
        pending: orders.value.filter(o => o.status === 'pending').length,
        paid: orders.value.filter(o => o.status === 'paid').length,
        completed: orders.value.filter(o => o.status === 'completed').length,
        cancelled: orders.value.filter(o => o.status === 'cancelled').length
      }
    } else {
      ElMessage.error(response.message || '获取订单列表失败')
      orders.value = []
      totalOrders.value = 0
    }
  } catch (error) {
    ElMessage.error('获取订单列表失败')
    console.error('获取订单列表错误:', error)
    orders.value = []
    totalOrders.value = 0
  } finally {
    loading.value = false
  }
}

// 分页变化处理
const handlePageChange = (page: number) => {
  currentPage.value = page
  fetchOrders()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  fetchOrders()
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
  fetchOrders()
}

// 日期变化处理
const handleDateChange = () => {
  currentPage.value = 1
  fetchOrders()
}

// 刷新
const handleRefresh = () => {
  currentPage.value = 1
  searchOrderOrCarQuery.value = ''
  searchUserQuery.value = ''
  statusFilter.value = ''
  dateRange.value = null
  fetchOrders()
}

// 查看详情
const handleViewDetail = (order: Order) => {
  selectedOrder.value = order
  showDetailDialog.value = true
}

// 修改状态
const handleChangeStatus = async (order: Order, newStatus: 'pending' | 'paid' | 'completed' | 'cancelled') => {
  try {
    await ElMessageBox.confirm(
        `确定要将订单状态修改为"${ORDER_STATUS_TEXT[newStatus]}"吗？`,
        '确认修改',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
    )
    let response: any;
    if (newStatus === 'cancelled') {
      response = await cancelOrders({
        id: order.id,
        status: newStatus
      })
    } else if (newStatus === 'paid') {
      response = await payOrders({
        id: order.id,
        status: newStatus
      })
    } else if (newStatus === 'completed') {
      response = await completeOrders({
        id: order.id,
        status: newStatus
      })
    }
    if (response.code === 0) {
      ElMessage.success('订单状态修改成功')
      await fetchOrders()
    } else {
      ElMessage.error(response.message || '修改订单状态失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('修改订单状态失败')
      console.error('修改订单状态错误:', error)
    }
  }
}

// 删除订单
const handleDeleteOrder = async (order: Order) => {
  try {
    await ElMessageBox.confirm(
        `确定要删除订单"${order.orderNumber || order.id}"吗？此操作不可恢复！`,
        '确认删除',
        {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          type: 'warning'
        }
    )

    const response = await deleteOrders(order.id)

    if (response.code === 0) {
      ElMessage.success('订单删除成功')
      await fetchOrders()
    } else {
      ElMessage.error(response.message || '删除订单失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除订单失败')
      console.error('删除订单错误:', error)
    }
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchOrders()
})
</script>

<style scoped>
.order-management {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 4px 0;
}

.page-subtitle {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

.stats-section {
  margin-bottom: 24px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
}

.stat-icon.pending {
  background: #E6A23C;
}

.stat-icon.paid {
  background: #67C23A;
}

.stat-icon.completed {
  background: #409EFF;
}

.stat-icon.cancelled {
  background: #F56C6C;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.filter-section {
  margin-bottom: 16px;
}

.car-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.car-name {
  font-weight: 500;
  color: #303133;
}

.car-details {
  font-size: 12px;
  color: #909399;
}

.rental-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.price {
  color: #FF6700;
  font-weight: 600;
}

.pagination-section {
  margin-top: 24px;
  display: flex;
  justify-content: center;
}

.order-detail {
  padding: 16px 0;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 12px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #409EFF;
}

/* 表格行样式 */
:deep(.pending-row) {
  background-color: #fdf6ec;
}

:deep(.paid-row) {
  background-color: #f0f9ff;
}

:deep(.completed-row) {
  background-color: #f0f9ff;
}

:deep(.cancelled-row) {
  background-color: #fef0f0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-section .el-row {
    margin: 0;
  }

  .stats-section .el-col {
    margin-bottom: 12px;
  }

  .filter-section .el-row {
    margin: 0;
  }

  .filter-section .el-col {
    margin-bottom: 12px;
  }
}
</style> 
<template>
  <div class="system-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">角色管理</h2>
        <p class="page-subtitle">管理系统角色和权限分配</p>
      </div>
      <div class="header-right">
        <el-button @click="refreshData" :loading="loading">
          <el-icon><Refresh/></el-icon>
          刷新数据
        </el-button>
        <el-button type="primary" @click="showRoleDialog = true">
          <el-icon><Plus/></el-icon>
          新增角色
        </el-button>
      </div>
    </div>

    <!-- 角色列表 -->
    <div class="table-section">
      <el-table
        :data="roles"
        v-loading="loading"
        style="width: 100%"
        :row-class-name="getRowClassName"
      >
        <el-table-column label="角色信息" min-width="200">
          <template #default="{ row }">
            <div class="role-info">
              <div class="role-name">{{ row.roleName }}</div>
              <div class="role-details">
                <span class="role-id">ID: {{ row.id }}</span>
                <span class="role-desc">{{ row.roleDescription || '暂无描述' }}</span>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="用户数量" width="120" align="center">
          <template #default="{ row }">
            <div class="user-count">
              <span class="count-number">{{ row.userCount || 0 }}</span>
              <span class="count-label">个用户</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.createTime) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="280" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="editRole(row)">
              <el-icon><Edit/></el-icon>
              编辑
            </el-button>
            <el-button size="small" type="info" @click="showRoleMenus(row)">
              <el-icon><Setting/></el-icon>
              管理菜单
            </el-button>
            <el-button
              size="small"
              type="danger"
              @click="deleteRoleClick(row.id)"
            >
              <el-icon><Delete/></el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 角色编辑对话框 -->
    <el-dialog
      v-model="showRoleDialog"
      :title="editingRole ? '编辑角色' : '新增角色'"
      width="500px"
    >
      <el-form :model="roleForm" label-width="80px">
        <el-form-item label="角色名称" required>
          <el-input
            v-model="roleForm.roleName"
            placeholder="请输入角色名称"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="角色描述">
          <el-input
            v-model="roleForm.roleDescription"
            type="textarea"
            placeholder="请输入角色描述"
            :rows="3"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showRoleDialog = false">取消</el-button>
          <el-button type="primary" @click="saveRole" :loading="saveLoading">
            保存
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 角色菜单管理对话框 -->
    <el-dialog
      v-model="showMenuDialog"
      title="管理角色菜单"
      width="700px"
      class="menu-dialog"
    >
      <div class="menu-dialog-content">
        <div class="role-info-section">
          <div class="role-info">
            <span class="role-name">{{ currentRole?.roleName }}</span>
            <span class="role-desc">{{ currentRole?.roleDescription }}</span>
          </div>
        </div>

        <div class="menu-section">
          <div class="section-header">
            <h4>菜单权限</h4>
            <p>选择该角色可以访问的菜单项</p>
          </div>
          
          <div class="menu-tree-container">
            <el-tree
              ref="menuTreeRef"
              :data="allMenus"
              :props="treeProps"
              :default-checked-keys="selectedMenus"
              node-key="id"
              show-checkbox
              check-strictly
              :expand-on-click-node="false"
              :default-expand-all="true"
              @check="handleMenuCheck"
            >
              <template #default="{ node, data }">
                <span class="menu-node">
                  <el-icon class="menu-icon">
                    <component :is="getMenuIcon(data.menuName)"/>
                  </el-icon>
                  <span class="menu-name">{{ data.menuName }}</span>
                  <span class="menu-route">{{ data.route || '无路由' }}</span>
                </span>
              </template>
            </el-tree>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="menu-dialog-footer">
          <div class="selection-info">
            <span>已选择 {{ selectedMenus.length }} 个菜单项</span>
          </div>
          <div class="dialog-actions">
            <el-button @click="showMenuDialog = false">取消</el-button>
            <el-button type="primary" @click="saveRoleMenus" :loading="menuSaveLoading">
              保存设置
            </el-button>
          </div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Plus, 
  Edit, 
  Delete, 
  Setting,
  Document,
  User,
  Van,
  DataBoard,
  Refresh
} from '@element-plus/icons-vue'
import { 
  list, 
  addRole, 
  updateRole, 
  deleteRole, 
  type AddRoleParams,
  type UpdateRoleParams 
} from '@/services/role'
import { 
  getAll, 
  getByRoleId, 
  updateRoleMenus, 
  type UpdateRoleMenusParams 
} from '@/services/menu'
import { queryUserPage, type QueryUserPageParams } from '@/services/userApi'

// 类型定义
interface Role {
  id: number
  roleName: string
  roleDescription: string
  userCount?: number
  createTime?: Record<string, unknown>
  updateTime?: Record<string, unknown>
}

interface Menu {
  id: number
  pid: number
  menuName: string
  route: string | null
  children?: Menu[] | null
  hasChildren?: boolean
  createTime?: Record<string, unknown>
  updateTime?: Record<string, unknown>
}

// 响应式数据
const loading = ref(false)
const saveLoading = ref(false)
const menuSaveLoading = ref(false)
const roles = ref<Role[]>([])
const allMenus = ref<Menu[]>([])
const selectedMenus = ref<number[]>([])

// 对话框状态
const showRoleDialog = ref(false)
const showMenuDialog = ref(false)
const editingRole = ref<Role | null>(null)
const currentRole = ref<Role | null>(null)

// 表单数据
const roleForm = ref({
  roleName: '',
  roleDescription: ''
})

// 树形组件配置
const treeProps = {
  children: 'children',
  label: 'menuName',
  hasChildren: 'hasChildren'
}

// 获取角色列表
const loadRoles = async () => {
  loading.value = true
  try {
    const response = await list()
    if (response.code === 0) {
      roles.value = response.data
      // 获取用户数量统计
      await loadUserCounts()
    } else {
      ElMessage.error(response.description || '获取角色列表失败')
    }
  } catch (error) {
    ElMessage.error('获取角色列表失败')
    console.error('获取角色列表错误:', error)
  } finally {
    loading.value = false
  }
}

// 获取用户数量统计
const loadUserCounts = async () => {
  try {
    // 获取所有用户（不分页）
    const params: QueryUserPageParams = {
      pageNum: 1,
      pageSize: 10000, // 设置一个很大的数字来获取所有用户
      params: {
        isDelete: 0 // 只获取未删除的用户
      }
    }
    
    const response = await queryUserPage(params)
    if (response.code === 0) {
      const users = response.data.list
      
      // 统计每个角色的用户数量
      const roleCounts = new Map<number, number>()
      
      // 初始化所有角色的计数为0
      roles.value.forEach(role => {
        roleCounts.set(role.id, 0)
      })
      
      // 统计每个用户的角色
      users.forEach(user => {
        const roleId = user.userRole
        if (roleCounts.has(roleId)) {
          roleCounts.set(roleId, roleCounts.get(roleId)! + 1)
        }
      })
      
      // 更新角色列表中的用户数量
      roles.value.forEach(role => {
        role.userCount = roleCounts.get(role.id) || 0
      })
      
      console.log('角色用户统计:', roleCounts)
      console.log('总用户数:', users.length)
    } else {
      console.error('获取用户列表失败:', response.description)
    }
  } catch (error) {
    console.error('获取用户数量统计错误:', error)
  }
}

// 获取所有菜单
const fetchAllMenus = async () => {
  try {
    const response = await getAll()
    if (response.code === 0) {
      allMenus.value = processMenuData(response.data)
    } else {
      ElMessage.error(response.description || '获取菜单列表失败')
    }
  } catch (error) {
    ElMessage.error('获取菜单列表失败')
    console.error('获取菜单列表错误:', error)
  }
}

// 处理菜单数据，设置hasChildren属性
const processMenuData = (menus: Menu[]): Menu[] => {
  const processMenu = (menuList: Menu[]) => {
    menuList.forEach(menu => {
      if (menu.children && menu.children.length > 0) {
        menu.hasChildren = true
        processMenu(menu.children)
      } else {
        menu.hasChildren = false
        menu.children = [] // 确保children是空数组而不是null
      }
    })
  }
  
  processMenu(menus)
  return menus
}

// 获取角色菜单
const fetchRoleMenus = async (roleId: number) => {
  try {
    const response = await getByRoleId(roleId)
    if (response.code === 0) {
      selectedMenus.value = response.data.map((menu: Menu) => menu.id)
    } else {
      ElMessage.error(response.description || '获取角色菜单失败')
      selectedMenus.value = []
    }
  } catch (error) {
    ElMessage.error('获取角色菜单失败')
    console.error('获取角色菜单错误:', error)
    selectedMenus.value = []
  }
}

// 显示角色菜单对话框
const showRoleMenus = async (role: Role) => {
  currentRole.value = role
  await fetchAllMenus()
  await fetchRoleMenus(role.id)
  showMenuDialog.value = true
}

// 处理菜单选择
const handleMenuCheck = (data: Menu, checkedInfo: any) => {
  selectedMenus.value = checkedInfo.checkedKeys
}

// 保存角色菜单
const saveRoleMenus = async () => {
  if (!currentRole.value) {
    ElMessage.error('未选择角色')
    return
  }

  menuSaveLoading.value = true
  try {
    const params: UpdateRoleMenusParams = {
      roleId: currentRole.value.id,
      menuIds: selectedMenus.value
    }

    console.log('发送的菜单数据:', params)
    const response = await updateRoleMenus(params)
    if (response.code === 0) {
      ElMessage.success('角色菜单更新成功')
      showMenuDialog.value = false
    } else {
      ElMessage.error(response.description || '角色菜单更新失败')
    }
  } catch (error) {
    ElMessage.error('角色菜单更新失败')
    console.error('保存角色菜单错误:', error)
  } finally {
    menuSaveLoading.value = false
  }
}

// 编辑角色
const editRole = (role: Role) => {
  editingRole.value = role
  roleForm.value = {
    roleName: role.roleName,
    roleDescription: role.roleDescription || ''
  }
  showRoleDialog.value = true
}

// 保存角色
const saveRole = async () => {
  if (!roleForm.value.roleName.trim()) {
    ElMessage.error('请输入角色名称')
    return
  }

  saveLoading.value = true
  try {
    if (editingRole.value) {
      // 更新角色
      const params: UpdateRoleParams = {
        id: editingRole.value.id,
        roleName: roleForm.value.roleName,
        roleDescription: roleForm.value.roleDescription
      }
      
      const response = await updateRole(params)
      if (response.code === 0) {
        ElMessage.success('角色更新成功')
        await loadRoles() // 这会重新加载角色列表和用户统计
        showRoleDialog.value = false
        resetRoleForm()
      } else {
        ElMessage.error(response.description || '角色更新失败')
      }
    } else {
      // 新增角色
      const params: AddRoleParams = {
        roleName: roleForm.value.roleName,
        roleDescription: roleForm.value.roleDescription
      }
      
      const response = await addRole(params)
      if (response.code === 0) {
        ElMessage.success('角色创建成功')
        await loadRoles() // 这会重新加载角色列表和用户统计
        showRoleDialog.value = false
        resetRoleForm()
      } else {
        ElMessage.error(response.description || '角色创建失败')
      }
    }
  } catch (error) {
    ElMessage.error('保存角色失败')
    console.error('保存角色错误:', error)
  } finally {
    saveLoading.value = false
  }
}

// 删除角色
const deleteRoleClick = async (roleId: number) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个角色吗？删除后不可恢复！',
      '确认删除',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await deleteRole(roleId)
    if (response.code === 0) {
      ElMessage.success('角色删除成功')
      await loadRoles() // 这会重新加载角色列表和用户统计
    } else {
      ElMessage.error(response.description || '角色删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('角色删除失败')
      console.error('删除角色错误:', error)
    }
  }
}

// 重置角色表单
const resetRoleForm = () => {
  editingRole.value = null
  roleForm.value = {
    roleName: '',
    roleDescription: ''
  }
}

// 获取菜单图标
const getMenuIcon = (menuName: string) => {
  switch (menuName) {
    case '仪表盘':
      return DataBoard
    case '用户管理':
      return User
    case '车辆管理':
      return Van
    case '订单管理':
      return Document
    default:
      return Document
  }
}

// 获取行样式
const getRowClassName = ({ row }: { row: Role }) => {
  return row.id === 1 ? 'admin-role-row' : ''
}

// 格式化日期时间
const formatDateTime = (dateTime: Record<string, unknown> | undefined) => {
  if (!dateTime) return '未知'
  try {
    const date = new Date(String(dateTime))
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    return '未知'
  }
}

// 刷新数据
const refreshData = async () => {
  await loadRoles()
}

// 组件挂载时加载数据
onMounted(() => {
  loadRoles()
})
</script>

<style scoped>
.system-container {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-height: calc(100vh - 120px);
}

/* 页面标题 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #E4E7ED;
}

.header-left .page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 4px 0;
}

.header-left .page-subtitle {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

/* 表格区域 */
.table-section {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

/* 角色信息样式 */
.role-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.role-name {
  font-weight: 600;
  color: #303133;
  font-size: 14px;
}

.role-details {
  display: flex;
  gap: 12px;
  align-items: center;
}

.role-id {
  color: #909399;
  font-size: 12px;
  background: #F5F7FA;
  padding: 2px 6px;
  border-radius: 4px;
}

.role-desc {
  color: #606266;
  font-size: 12px;
}

/* 用户数量样式 */
.user-count {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.count-number {
  color: #409EFF;
  font-weight: 600;
  font-size: 18px;
}

.count-label {
  color: #909399;
  font-size: 12px;
}

/* 管理员角色行样式 */
:deep(.admin-role-row) {
  background-color: #f0f9ff;
}

/* 对话框样式 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 菜单对话框样式 */
.menu-dialog {
  .el-dialog__body {
    padding: 0;
  }
}

.menu-dialog-content {
  padding: 24px;
}

.role-info-section {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #E4E7ED;
}

.role-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.role-info .role-name {
  font-size: 16px;
  font-weight: 600;
  color: #409EFF;
}

.role-info .role-desc {
  color: #606266;
  font-size: 14px;
}

.menu-section {
  margin-bottom: 20px;
}

.section-header {
  margin-bottom: 16px;
}

.section-header h4 {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 4px 0;
}

.section-header p {
  color: #909399;
  font-size: 12px;
  margin: 0;
}

.menu-tree-container {
  border: 1px solid #E4E7ED;
  border-radius: 8px;
  padding: 16px;
  max-height: 400px;
  overflow-y: auto;
}



.menu-node {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.menu-icon {
  color: #409EFF;
  font-size: 14px;
}

.menu-name {
  font-weight: 500;
  color: #303133;
  font-size: 14px;
}

.menu-route {
  color: #909399;
  font-size: 12px;
  font-family: monospace;
  margin-left: auto;
}

.menu-dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-top: 1px solid #E4E7ED;
  background: #FAFAFA;
}

.selection-info {
  color: #606266;
  font-size: 14px;
}

.dialog-actions {
  display: flex;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .system-container {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .header-right {
    width: 100%;
  }

  .header-right .el-button {
    width: 100%;
  }

  .menu-dialog {
    width: 95% !important;
  }

  .menu-dialog-content {
    padding: 16px;
  }

  .menu-dialog-footer {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .dialog-actions {
    justify-content: flex-end;
  }
}
</style> 
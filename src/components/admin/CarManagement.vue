<template>
  <div class="car-management">
    <!-- 页面标题和操作栏 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">车辆管理</h2>
        <p class="page-subtitle">管理所有租赁车辆信息</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="showAddDialog = true">
          <el-icon><Plus /></el-icon>
          添加车辆
        </el-button>
      </div>
    </div>

    <!-- 筛选栏 -->
    <div class="filter-section">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-input
            v-model="searchQuery"
            placeholder="搜索车牌号或车型"
            clearable
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-select v-model="statusFilter" placeholder="状态筛选" clearable>
            <el-option label="全部" value="" />
            <el-option label="可租" value="available" />
            <el-option label="维修中" value="maintenance" />
            <el-option label="已租出" value="rented" />
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-button @click="handleRefresh">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 批量操作栏 -->
    <div v-if="selectedCars.length > 0" class="batch-actions">
      <el-alert
        :title="`已选择 ${selectedCars.length} 辆车`"
        type="info"
        show-icon
        closable
      >
        <template #default>
          <el-button size="small" type="success" @click="handleBatchEnable">
            批量上架
          </el-button>
          <el-button size="small" type="warning" @click="handleBatchDisable">
            批量下架
          </el-button>
          <el-button size="small" type="danger" @click="handleBatchDelete">
            批量删除
          </el-button>
        </template>
      </el-alert>
    </div>

    <!-- 车辆列表 -->
    <div class="table-section">
      <el-table
        :data="filteredCars"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        style="width: 100%"
        :row-class-name="getRowClassName"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column label="缩略图" width="100">
          <template #default="{ row }">
            <div class="car-thumbnail">
              <el-image
                :src="row.image || ''"
                fit="cover"
                :preview-src-list="[row.image]"
              >
                <template #error>
                  <div class="image-placeholder">
                    <el-icon><Van /></el-icon>
                  </div>
                </template>
              </el-image>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="plateNumber" label="车牌号" width="120">
          <template #default="{ row }">
            {{ row.plateNumber || '未分配' }}
          </template>
        </el-table-column>
        
        <el-table-column prop="name" label="车型" min-width="150" />
        
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="dailyRate" label="日租金" width="100">
          <template #default="{ row }">
            <span class="price">¥{{ row.dailyRate }}</span>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="handleEdit(row)">编辑</el-button>
            <el-button 
              size="small" 
              type="danger" 
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[5, 10, 20, 50]"
          :total="totalCars"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 添加/编辑车辆对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="editingCar ? '编辑车辆信息' : '添加新车辆'"
      width="800px"
      :before-close="handleDialogClose"
      class="car-form-dialog"
      top="5vh"
    >
      <div class="dialog-header">
        <div class="header-icon">
          <el-icon size="24">
            <Van />
          </el-icon>
        </div>
        <div class="header-content">
          <h3 class="dialog-title">{{ editingCar ? '编辑车辆信息' : '添加新车辆' }}</h3>
          <p class="dialog-subtitle">{{ editingCar ? '修改车辆的基本信息和配置' : '填写车辆的基本信息和配置' }}</p>
        </div>
      </div>

      <el-form
        ref="carFormRef"
        :model="carForm"
        :disabled="saving"
        class="car-form"
      >
        <!-- 基本信息 -->
        <div class="form-section">
          <div class="section-header">
            <el-icon><InfoFilled /></el-icon>
            <span>基本信息</span>
          </div>
          <el-row>
            <el-col :span="12">
              <el-form-item label="车型名称" prop="name">
                <el-input 
                  v-model="carForm.name" 
                  placeholder="请输入车型名称，如：奥迪A6L"
                  maxlength="50"
                  show-word-limit
                  clearable
                />
              </el-form-item>
            </el-col>
            
            <el-col :span="12">
              <el-form-item label="品牌" prop="brand">
                <el-input 
                  v-model="carForm.brand" 
                  placeholder="请输入品牌，如：奥迪"
                  maxlength="30"
                  show-word-limit
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="发动机" prop="engine">
                <el-input 
                  v-model="carForm.engine" 
                  placeholder="如：2.0T、1.5L"
                  maxlength="20"
                  clearable
                />
              </el-form-item>
            </el-col>
            
            <el-col :span="12">
              <el-form-item label="座位数" prop="seats">
                <el-input-number 
                  v-model="carForm.seats" 
                  :min="1" 
                  :max="10"
                  style="width: 100%"
                  placeholder="选择座位数"
                  controls-position="right"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 技术参数 -->
        <div class="form-section">
          <div class="section-header">
            <el-icon><Setting /></el-icon>
            <span>技术参数</span>
          </div>
          <el-row>
            <el-col :span="12">
              <el-form-item label="变速箱" prop="transmission">
                <el-select v-model="carForm.transmission" placeholder="请选择变速箱类型" style="width: 100%">
                  <el-option label="自动" value="自动" />
                  <el-option label="手动" value="手动" />
                  <el-option label="CVT" value="CVT" />
                  <el-option label="双离合" value="双离合" />
                </el-select>
              </el-form-item>
            </el-col>
            
            <el-col :span="12">
              <el-form-item label="燃油类型" prop="fuelType">
                <el-select v-model="carForm.fuelType" placeholder="请选择燃油类型" style="width: 100%">
                  <el-option label="汽油" value="汽油" />
                  <el-option label="柴油" value="柴油" />
                  <el-option label="电动" value="电动" />
                  <el-option label="混合动力" value="混合动力" />
                  <el-option label="插电混动" value="插电混动" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item label="生产年份" prop="year">
                <el-input-number 
                  v-model="carForm.year" 
                  :min="1900" 
                  :max="new Date().getFullYear() + 1"
                  style="width: 100%"
                  placeholder="选择年份"
                  controls-position="right"
                />
              </el-form-item>
            </el-col>
            
            <el-col :span="12">
              <el-form-item label="行驶里程" prop="mileage">
                <el-input-number 
                  v-model="carForm.mileage" 
                  :min="0"
                  :step="1000"
                  style="width: 100%"
                  placeholder="输入里程数(公里)"
                  controls-position="right"
                >
                  <template #suffix>公里</template>
                </el-input-number>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 租赁信息 -->
        <div class="form-section">
          <div class="section-header">
            <el-icon><Money /></el-icon>
            <span>租赁信息</span>
          </div>
          <el-row>
            <el-col :span="12">
              <el-form-item label="日租金" prop="dailyRate">
                <el-input-number 
                  v-model="carForm.dailyRate" 
                  :min="0" 
                  :precision="2"
                  :step="10"
                  style="width: 100%"
                  placeholder="输入日租金"
                  controls-position="right"
                >
                  <template #prefix>¥</template>
                </el-input-number>
              </el-form-item>
            </el-col>
            
            <el-col :span="12">
              <el-form-item label="车辆状态" prop="status">
                <el-select v-model="carForm.status" placeholder="请选择状态" style="width: 100%">
                  <el-option label="可租" value="available" />
                  <el-option label="维修中" value="maintenance" />
                  <el-option label="已租出" value="rented" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 图片信息 -->
        <div class="form-section">
          <div class="section-header">
            <el-icon><Picture /></el-icon>
            <span>图片信息</span>
          </div>
          <el-form-item label="车辆图片" prop="image">
            <div class="image-upload-section">
              <el-input 
                v-model="carForm.image" 
                placeholder="请输入图片URL地址"
                @input="handleImageInput"
                clearable
              >
                <template #prepend>
                  <el-icon><Link /></el-icon>
                </template>
                <template #append>
                  <el-button @click="showImageUpload = true" :disabled="!carForm.image" type="primary" plain>
                    预览
                  </el-button>
                </template>
              </el-input>
              <div class="image-preview" v-if="carForm.image">
                <div class="preview-label">图片预览：</div>
                <el-image
                  :src="carForm.image"
                  fit="cover"
                  class="preview-image"
                  :preview-src-list="[carForm.image]"
                >
                  <template #error>
                    <div class="image-error">
                      <el-icon><Picture /></el-icon>
                      <span>加载失败</span>
                    </div>
                  </template>
                </el-image>
              </div>
            </div>
          </el-form-item>
        </div>

        <!-- 备注信息 -->
        <div class="form-section">
          <div class="section-header">
            <el-icon><Document /></el-icon>
            <span>备注信息</span>
          </div>
          <el-form-item label="备注说明" prop="remark">
            <el-input 
              v-model="carForm.remark" 
              type="textarea" 
              :rows="3"
              placeholder="请输入车辆备注信息（可选）"
              maxlength="200"
              show-word-limit
              resize="none"
            />
          </el-form-item>
        </div>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleDialogClose" :disabled="saving" size="large">
            <el-icon><Close /></el-icon>
            取消
          </el-button>
          <el-button type="primary" @click="handleSaveCar" :loading="saving" size="large">
            <el-icon><Check /></el-icon>
            {{ editingCar ? '更新车辆' : '添加车辆' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Plus, 
  Search, 
  Refresh, 
  Van,
  Picture,
  InfoFilled,
  Setting,
  Money,
  Link,
  Document,
  Close,
  Check
} from '@element-plus/icons-vue'
import { 
  queryCarPage, 
  addCar, 
  updateCar, 
  deleteCar, 
  deleteCarBatch, 
  updateCarStatus,
  type QueryCarPageParams,
  type AddCarParams,
  type UpdateCarParams
} from '@/services/carApi.ts'
import { transformCarData, CAR_STATUS } from '@/config/api.ts'

// 车辆接口类型定义
interface Car {
  id: number
  name: string
  engine: string
  seats: number
  transmission: string
  dailyRate: number
  brand: string
  year: number
  mileage: number
  fuelType: string
  available: boolean
  image: string
  createdAt: string
  updatedAt: string
  isDelete: number
  plateNumber: string
  status: string
  remark?: string
}

// 响应式数据
const loading = ref(false)
const saving = ref(false)
const showAddDialog = ref(false)
const showImageUpload = ref(false)
const editingCar = ref<Car | null>(null)
const selectedCars = ref<Car[]>([])
const searchQuery = ref('')
const statusFilter = ref('')
const currentPage = ref(1)
const pageSize = ref(5)
const totalCars = ref(0)

// 表单数据
const carFormRef = ref()
const carForm = ref({
  name: '',
  brand: '',
  engine: '',
  seats: 5,
  transmission: '自动',
  dailyRate: 0,
  status: 'available',
  image: '',
  year: new Date().getFullYear(),
  mileage: 0,
  fuelType: '汽油',
  remark: ''
})



// 模拟车辆数据
const cars = ref<Car[]>([])

// 计算属性
const filteredCars = computed(() => {
  let filtered = cars.value

  // 搜索筛选
  if (searchQuery.value) {
    filtered = filtered.filter(car => 
      car.plateNumber?.includes(searchQuery.value) ||
      car.name.includes(searchQuery.value)
    )
  }

  // 状态筛选
  if (statusFilter.value) {
    filtered = filtered.filter(car => car.status === statusFilter.value)
  }

  return filtered
})

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case 'available': return 'success'
    case 'maintenance': return 'warning'
    case 'rented': return 'info'
    default: return 'info'
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'available': return '可租'
    case 'maintenance': return '维修中'
    case 'rented': return '已租出'
    default: return '未知'
  }
}

// 获取行样式
const getRowClassName = ({ row }: { row: Car }) => {
  if (row.status === 'maintenance') return 'maintenance-row'
  if (row.status === 'rented') return 'rented-row'
  return ''
}

// 获取车辆列表
const fetchCars = async () => {
  loading.value = true
  try {
    const params: QueryCarPageParams = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      params: {
        isDelete: 0 // 只查询未删除的车辆
      }
    }

    // 搜索条件
    if (searchQuery.value.trim()) {
      params.params!.name = searchQuery.value.trim()
    }

    // 状态筛选
    if (statusFilter.value) {
      if (statusFilter.value === 'available') {
        params.params!.available = CAR_STATUS.AVAILABLE
      } else if (statusFilter.value === 'rented') {
        params.params!.available = CAR_STATUS.UNAVAILABLE
      }
    }

    const response = await queryCarPage(params)

    if (response.code === 0 && response.data) {
      // 转换车辆数据并添加车牌号和状态
      cars.value = response.data.list.map((car, index) => {
        const transformedCar = transformCarData(car)
        return {
          ...transformedCar,
          plateNumber: `京A${String(index + 1).padStart(5, '0')}`,
          status: car.available === CAR_STATUS.AVAILABLE ? 'available' : 'rented',
          createdAt: String(car.createdAt),
          updatedAt: String(car.updatedAt),
          isDelete: car.isDelete
        } as Car
      })
      totalCars.value = response.data.total
    } else {
      ElMessage.error(response.message || '获取车辆列表失败')
      cars.value = []
      totalCars.value = 0
    }
  } catch (error) {
    ElMessage.error('获取车辆列表失败')
    console.error('获取车辆列表错误:', error)
    cars.value = []
    totalCars.value = 0
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
  fetchCars()
}

// 刷新
const handleRefresh = () => {
  currentPage.value = 1
  searchQuery.value = ''
  statusFilter.value = ''
  fetchCars()
}

// 选择变化
const handleSelectionChange = (selection: Car[]) => {
  selectedCars.value = selection
}

// 编辑车辆
const handleEdit = (car: Car) => {
  editingCar.value = car
  carForm.value = { 
    name: car.name,
    brand: car.brand,
    engine: car.engine,
    seats: car.seats,
    transmission: car.transmission,
    dailyRate: car.dailyRate,
    status: car.status,
    image: car.image,
    year: car.year,
    mileage: car.mileage,
    fuelType: car.fuelType,
    remark: car.remark || ''
  }
  showAddDialog.value = true
}

// 删除车辆
const handleDelete = async (car: Car) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除车辆 ${car.name} 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const response = await deleteCar(car.id)
    if (response.code === 0) {
      ElMessage.success('删除成功')
      await fetchCars()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
      console.error('删除车辆错误:', error)
    }
  }
}

// 保存车辆
const handleSaveCar = async () => {
  try {
    saving.value = true
    
    if (editingCar.value) {
      // 更新车辆
      const updateParams: UpdateCarParams = {
        id: editingCar.value.id,
        name: carForm.value.name,
        brand: carForm.value.brand,
        engine: carForm.value.engine,
        seats: carForm.value.seats,
        transmission: carForm.value.transmission,
        dailyRate: carForm.value.dailyRate,
        year: carForm.value.year,
        mileage: carForm.value.mileage,
        fuelType: carForm.value.fuelType,
        image: carForm.value.image,
        available: carForm.value.status === 'available' ? CAR_STATUS.AVAILABLE : CAR_STATUS.UNAVAILABLE
      }
      
      const response = await updateCar(updateParams)
      if (response.code === 0) {
        ElMessage.success('更新成功')
        await fetchCars()
      } else {
        ElMessage.error(response.message || '更新失败')
      }
    } else {
      // 添加车辆
      const addParams: AddCarParams = {
        name: carForm.value.name,
        brand: carForm.value.brand,
        engine: carForm.value.engine,
        seats: carForm.value.seats,
        transmission: carForm.value.transmission,
        dailyRate: carForm.value.dailyRate,
        year: carForm.value.year,
        mileage: carForm.value.mileage,
        fuelType: carForm.value.fuelType,
        image: carForm.value.image,
        available: carForm.value.status === 'available' ? CAR_STATUS.AVAILABLE : CAR_STATUS.UNAVAILABLE
      }
      
      const response = await addCar(addParams)
      if (response.code === 0) {
        ElMessage.success('添加成功')
        await fetchCars()
      } else {
        ElMessage.error(response.message || '添加失败')
      }
    }
    
    showAddDialog.value = false
    resetForm()
  } catch (error) {
    ElMessage.error('保存失败')
    console.error('保存车辆错误:', error)
  } finally {
    saving.value = false
  }
}

// 重置表单
const resetForm = () => {
  carForm.value = {
    name: '',
    brand: '',
    engine: '',
    seats: 5,
    transmission: '自动',
    dailyRate: 0,
    status: 'available',
    image: '',
    year: new Date().getFullYear(),
    mileage: 0,
    fuelType: '汽油',
    remark: ''
  }
  editingCar.value = null
}

// 对话框关闭处理
const handleDialogClose = () => {
  if (saving.value) return false
  showAddDialog.value = false
  resetForm()
}

// 图片输入处理
const handleImageInput = () => {
  // 可以在这里添加图片URL验证逻辑
}

// 批量操作
const handleBatchEnable = async () => {
  try {
    const promises = selectedCars.value.map(car => 
      updateCarStatus(car.id, CAR_STATUS.AVAILABLE)
    )
    await Promise.all(promises)
    ElMessage.success('批量上架成功')
    await fetchCars()
    selectedCars.value = []
  } catch (error) {
    ElMessage.error('批量上架失败')
    console.error('批量上架错误:', error)
  }
}

const handleBatchDisable = async () => {
  try {
    const promises = selectedCars.value.map(car => 
      updateCarStatus(car.id, CAR_STATUS.UNAVAILABLE)
    )
    await Promise.all(promises)
    ElMessage.success('批量下架成功')
    await fetchCars()
    selectedCars.value = []
  } catch (error) {
    ElMessage.error('批量下架失败')
    console.error('批量下架错误:', error)
  }
}

const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedCars.value.length} 辆车吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const ids = selectedCars.value.map(car => car.id)
    const response = await deleteCarBatch(ids)
    if (response.code === 0) {
      ElMessage.success('批量删除成功')
      await fetchCars()
      selectedCars.value = []
    } else {
      ElMessage.error(response.message || '批量删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
      console.error('批量删除错误:', error)
    }
  }
}

// 分页处理
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  fetchCars()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchCars()
}

onMounted(() => {
  fetchCars()
})
</script>

<style scoped>
.car-management {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 4px 0;
}

.page-subtitle {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

.filter-section {
  margin-bottom: 16px;
}

.batch-actions {
  margin-bottom: 16px;
}

.car-thumbnail {
  width: 60px;
  height: 40px;
  border-radius: 4px;
  overflow: hidden;
}

.image-placeholder {
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #909399;
}

.price {
  color: #FF6700;
  font-weight: 600;
}

.pagination-section {
  margin-top: 24px;
  display: flex;
  justify-content: center;
}

/* 表格行样式 */
:deep(.maintenance-row) {
  background-color: #fdf6ec;
}

:deep(.rented-row) {
  background-color: #f0f9ff;
}

/* 车辆表单对话框样式 */
.car-form-dialog {
  border-radius: 12px;
  overflow: hidden;
}

.car-form-dialog :deep(.el-dialog__header) {
  display: none;
}

.car-form-dialog :deep(.el-dialog__body) {
  padding: 0;
}

.car-form-dialog :deep(.el-dialog__footer) {
  padding: 20px 24px;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
}

/* 对话框头部 */
.dialog-header {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 24px 24px 0;
  margin-bottom: 24px;
}

.header-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.header-content {
  flex: 1;
}

.dialog-title {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 4px 0;
}

.dialog-subtitle {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

/* 表单样式 */
.car-form {
  padding: 0 16px 24px;
}

/* 调整表单左右分布 */
.car-form :deep(.el-form-item) {
  margin-bottom: 20px;
  display: flex;
  align-items: flex-start;
}

/* 让左边列靠左，右边列靠右，整体更均匀 */
.car-form :deep(.el-row) {
  margin: 0;
  display: flex;
  justify-content: space-between;
}

.car-form :deep(.el-col) {
  flex: 0 0 calc(50% - 8px);
  max-width: calc(50% - 8px);
  padding: 0;
}

.form-section {
  margin-bottom: 32px;
  background: #fafafa;
  border-radius: 8px;
  padding: 16px 20px;
  border: 1px solid #f0f0f0;
}

.form-section:last-child {
  margin-bottom: 0;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 2px solid #e6e6e6;
  color: #606266;
  font-weight: 600;
  font-size: 16px;
}

.section-header .el-icon {
  color: #409EFF;
  font-size: 18px;
}

/* 表单项样式优化 */
.car-form :deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
  text-align: right;
  width: 100px;
  padding-right: 12px;
}

/* 隐藏必填星号 */
.car-form :deep(.el-form-item__label::before) {
  display: none !important;
}

.car-form :deep(.el-form-item__content) {
  flex: 1;
  min-width: 0;
}

/* 确保所有输入框长度一致 */
.car-form :deep(.el-input),
.car-form :deep(.el-input-number),
.car-form :deep(.el-select) {
  width: 100%;
}

.car-form :deep(.el-input__wrapper) {
  border-radius: 6px;
  box-shadow: 0 0 0 1px #dcdfe6 inset;
  transition: all 0.3s ease;
}

.car-form :deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #c0c4cc inset;
}

.car-form :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #409EFF inset;
}

.car-form :deep(.el-input-number .el-input__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6 inset;
}

.car-form :deep(.el-select .el-input__wrapper) {
  box-shadow: 0 0 0 1px #dcdfe6 inset;
}

/* 图片上传相关样式 */
.image-upload-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.image-preview {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e6e6e6;
}

.preview-label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.preview-image {
  width: 120px;
  height: 72px;
  border-radius: 6px;
  border: 1px solid #e6e6e6;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 120px;
  height: 72px;
  background: #f5f7fa;
  border-radius: 6px;
  color: #909399;
  font-size: 12px;
  border: 1px dashed #d9d9d9;
}

/* 对话框底部 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
}

.dialog-footer .el-button {
  border-radius: 6px;
  font-weight: 500;
  padding: 12px 24px;
  transition: all 0.3s ease;
}

.dialog-footer .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 响应式优化 */
@media (max-width: 768px) {
  .car-form-dialog {
    width: 95% !important;
    margin: 2.5vh auto;
  }
  
  .dialog-header {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }
  
  .form-section {
    padding: 16px;
  }
  
  .car-form {
    padding: 0 16px 16px;
  }
  
  .dialog-footer {
    flex-direction: column;
  }
  
  .dialog-footer .el-button {
    width: 100%;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .filter-section .el-row {
    margin: 0;
  }
  
  .filter-section .el-col {
    margin-bottom: 12px;
  }
}
</style> 
<template>
  <div class="user-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-left">
        <h2 class="page-title">用户管理</h2>
        <p class="page-subtitle">管理所有注册用户</p>
      </div>
    </div>

    <!-- 筛选栏 -->
    <div class="filter-section">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-input
              v-model="searchQuery"
              placeholder="搜索用户名或邮箱"
              clearable
              @input="handleSearch"
          >
            <template #prefix>
              <el-icon>
                <Search/>
              </el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="4">
          <el-select v-model="statusFilter" placeholder="状态筛选" clearable>
            <el-option label="全部" value=""/>
            <el-option label="正常" value="active"/>
            <el-option label="禁用" value="inactive"/>
            <el-option label="锁定" value="locked"/>
          </el-select>
        </el-col>
        <el-col :span="4">
          <el-button @click="handleRefresh">
            <el-icon>
              <Refresh/>
            </el-icon>
            刷新
          </el-button>
        </el-col>
      </el-row>
    </div>

    <!-- 用户列表 -->
    <div class="table-section">
      <el-table
          :data="filteredUsers"
          v-loading="loading"
          style="width: 100%"
          :row-class-name="getRowClassName"
      >
        <el-table-column label="头像" width="80">
          <template #default="{ row }">
            <el-avatar :size="40" :src="row.avatarUrl">
              <el-icon>
                <User/>
              </el-icon>
            </el-avatar>
          </template>
        </el-table-column>

        <el-table-column label="用户信息" min-width="200">
          <template #default="{ row }">
            <div class="user-info">
              <div class="username">{{ row.username }}</div>
              <div class="email">{{ row.email }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="注册时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.registerTime) }}
          </template>
        </el-table-column>

        <el-table-column label="最后登录" width="180">
          <template #default="{ row }">
            {{ formatDate(row.lastLoginTime) }}
          </template>
        </el-table-column>

        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="handleViewDetail(row)">
              查看详情
            </el-button>
            <el-switch
                v-model="row.status"
                :active-value="'active'"
                :inactive-value="'inactive'"
                @change="(value: 'active' | 'inactive') => handleStatusChange(row, value)"
            />
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[5,10,20,50]"
            :total="totalUsers"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 用户详情对话框 -->
    <el-dialog
        v-model="showDetailDialog"
        title="用户详情"
        width="600px"
    >
      <div v-if="selectedUser" v-loading="detailLoading" class="user-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="用户ID">
            {{ selectedUser.id }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(selectedUser.status)">
              {{ getStatusText(selectedUser.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="用户名">
            {{ selectedUser.username }}
          </el-descriptions-item>
          <el-descriptions-item label="账号">
            {{ selectedUser.userAccount }}
          </el-descriptions-item>
          <el-descriptions-item label="邮箱">
            {{ selectedUser.email }}
          </el-descriptions-item>
          <el-descriptions-item label="手机号">
            {{ selectedUser.phone || '未设置' }}
          </el-descriptions-item>
          <el-descriptions-item label="性别">
            {{ selectedUser.gender === 0 ? '男' : selectedUser.gender === 1 ? '女' : '未知' }}
          </el-descriptions-item>
          <el-descriptions-item label="用户角色">
            {{ selectedUser.userRole === 0 ? '普通用户' : selectedUser.userRole === 1 ? '管理员' : '未知' }}
          </el-descriptions-item>
          <el-descriptions-item label="注册时间">
            {{ formatDate(selectedUser.registerTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="最后登录">
            {{ formatDate(selectedUser.lastLoginTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间">
            {{ formatDate(selectedUser.updateTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="登录次数">
            {{ selectedUser.loginCount || 0 }}次
          </el-descriptions-item>
        </el-descriptions>

        <!-- 用户订单统计 -->
        <div class="user-stats">
          <h4>订单统计</h4>
          <div v-if="selectedUser.orderCount !== undefined && selectedUser.orderCount > 0">
            <el-row :gutter="16">
              <el-col :span="6">
                <div class="stat-item">
                  <div class="stat-number">{{ selectedUser.orderCount }}</div>
                  <div class="stat-label">总订单数</div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="stat-item">
                  <div class="stat-number">¥{{ selectedUser.totalSpent?.toFixed(2) }}</div>
                  <div class="stat-label">总消费</div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="stat-item">
                  <div class="stat-number">¥{{ selectedUser.avgOrderAmount?.toFixed(2) }}</div>
                  <div class="stat-label">平均订单金额</div>
                </div>
              </el-col>
              <el-col :span="6">
                <div class="stat-item">
                  <div class="stat-number last-order-date">{{ selectedUser.lastOrderDate || '无' }}</div>
                  <div class="stat-label">最后订单日期</div>
                </div>
              </el-col>
            </el-row>
          </div>
          <div v-else-if="selectedUser.orderCount === 0" class="no-data">
            <el-empty description="暂无订单数据"/>
          </div>
          <div v-else class="no-data">
            <el-empty description="加载中..."/>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import {ref, computed, onMounted} from 'vue'
import {ElMessage, ElMessageBox} from 'element-plus'
import {
  Search,
  Refresh,
  User
} from '@element-plus/icons-vue'
import {
  queryUserPage,
  updateUser,
  deleteUser,
  deleteUserBatch,
  type QueryUserPageParams,
  type UpdateUserParams
} from '@/services/userApi.ts'
import {
  queryOrdersPage,
  type QueryOrdersPageParams
} from '@/services/ordersApi.ts'

// 用户接口
interface User {
  id: number
  username: string
  userAccount: string
  email: string
  phone: string
  avatarUrl: string
  gender: number
  userStatus: number
  userRole: number
  createTime: string
  updateTime: string
  lastLoginTime: string
  isDelete: number
  // 扩展字段，用于显示
  status: 'active' | 'inactive' | 'locked'
  registerTime: string
  loginCount?: number
  orderCount?: number
  totalSpent?: number
  avgOrderAmount?: number
  lastOrderDate?: string
}

// 响应式数据
const loading = ref(false)
const showDetailDialog = ref(false)
const selectedUser = ref<User | null>(null)
const detailLoading = ref(false)
const searchQuery = ref('')
const statusFilter = ref('')
const currentPage = ref(1)
const pageSize = ref(5)
const totalUsers = ref(0)

// 用户数据
const users = ref<User[]>([])

// 计算属性
const filteredUsers = computed(() => {
  let filtered = users.value

  // 搜索筛选
  if (searchQuery.value) {
    filtered = filtered.filter(user =>
        user.username.includes(searchQuery.value) ||
        user.email.includes(searchQuery.value)
    )
  }

  // 状态筛选
  if (statusFilter.value) {
    filtered = filtered.filter(user => user.status === statusFilter.value)
  }

  return filtered
})

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case 'active':
      return 'success'
    case 'inactive':
      return 'warning'
    case 'locked':
      return 'danger'
    default:
      return 'info'
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'active':
      return '正常'
    case 'inactive':
      return '禁用'
    case 'locked':
      return '锁定'
    default:
      return '未知'
  }
}

// 获取行样式
const getRowClassName = ({row}: { row: User }) => {
  if (row.status === 'inactive') return 'inactive-row'
  if (row.status === 'locked') return 'locked-row'
  return ''
}

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 获取用户列表
const fetchUsers = async () => {
  loading.value = true
  try {
    const params: QueryUserPageParams = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      params: {
        isDelete: 0 // 只查询未删除的用户
      }
    }

    // 搜索条件
    if (searchQuery.value.trim()) {
      params.params!.username = searchQuery.value.trim()
    }

    // 状态筛选
    if (statusFilter.value) {
      if (statusFilter.value === 'active') {
        params.params!.userStatus = 0 // 0表示正常
      } else if (statusFilter.value === 'inactive') {
        params.params!.userStatus = 1 // 1表示禁用
      } else if (statusFilter.value === 'locked') {
        params.params!.userStatus = 2 // 2表示锁定
      }
    }

    const response = await queryUserPage(params)

    if (response.code === 0 && response.data) {
      // 转换用户数据
      users.value = response.data.list.map(user => ({
        ...user,
        createTime: String(user.createTime),
        updateTime: String(user.updateTime),
        lastLoginTime: String(user.lastLoginTime),
        status: user.userStatus === 0 ? 'active' : user.userStatus === 1 ? 'inactive' : 'locked',
        registerTime: String(user.createTime),
        // 统计数据（在查看详情时动态获取）
        loginCount: 0,
        orderCount: undefined,
        totalSpent: undefined,
        avgOrderAmount: undefined,
        lastOrderDate: undefined
      } as User))
      totalUsers.value = response.data.total
    } else {
      ElMessage.error(response.message || '获取用户列表失败')
      users.value = []
      totalUsers.value = 0
    }
  } catch (error) {
    ElMessage.error('获取用户列表失败')
    console.error('获取用户列表错误:', error)
    users.value = []
    totalUsers.value = 0
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1
  fetchUsers()
}

// 刷新
const handleRefresh = () => {
  currentPage.value = 1
  searchQuery.value = ''
  statusFilter.value = ''
  fetchUsers()
}

// 获取用户订单统计数据
const getUserOrderStats = async (userId: number) => {
  try {
    const params: QueryOrdersPageParams = {
      pageNum: 1,
      pageSize: 5, // 获取所有订单
      params: {
        userId: userId,
        isDelete: 0
      }
    }

    const response = await queryOrdersPage(params)

    if (response.code === 0 && response.data) {
      const orders = response.data.list

      if (orders.length > 0) {
        // 计算统计数据
        const totalAmount = orders.reduce((sum, order) => sum + order.totalAmount, 0)
        const avgAmount = totalAmount / orders.length
        const lastOrder = orders.sort((a, b) =>
            new Date(String(b.createdAt)).getTime() - new Date(String(a.createdAt)).getTime()
        )[0]

        return {
          orderCount: orders.length,
          totalSpent: totalAmount,
          avgOrderAmount: Math.round(avgAmount * 100) / 100,
          lastOrderDate: String(lastOrder.createdAt).split('T')[0]
        }
      }
    }

    return {
      orderCount: 0,
      totalSpent: 0,
      avgOrderAmount: 0,
      lastOrderDate: undefined
    }
  } catch (error) {
    console.error('获取用户订单统计错误:', error)
    return {
      orderCount: 0,
      totalSpent: 0,
      avgOrderAmount: 0,
      lastOrderDate: undefined
    }
  }
}

// 查看详情
const handleViewDetail = async (user: User) => {
  selectedUser.value = user
  showDetailDialog.value = true
  detailLoading.value = true

  try {
    // 获取用户订单统计数据
    const orderStats = await getUserOrderStats(user.id)

    // 更新用户数据
    if (selectedUser.value) {
      selectedUser.value.orderCount = orderStats.orderCount
      selectedUser.value.totalSpent = orderStats.totalSpent
      selectedUser.value.avgOrderAmount = orderStats.avgOrderAmount
      selectedUser.value.lastOrderDate = orderStats.lastOrderDate
    }
  } catch (error) {
    ElMessage.error('获取用户订单数据失败')
    console.error('获取用户订单数据错误:', error)
  } finally {
    detailLoading.value = false
  }
}

// 状态变化
const handleStatusChange = async (user: User, newStatus: 'active' | 'inactive' | 'locked') => {
  try {
    const statusText = newStatus === 'active' ? '启用' : newStatus === 'inactive' ? '禁用' : '锁定'
    await ElMessageBox.confirm(
        `确定要${statusText}用户 ${user.username} 吗？`,
        '确认操作',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
    )

    const updateParams: UpdateUserParams = {
      id: user.id,
      userStatus: newStatus === 'active' ? 0 : newStatus === 'inactive' ? 1 : 2
    }

    const response = await updateUser(updateParams)
    if (response.code === 0) {
      user.status = newStatus
      user.userStatus = newStatus === 'active' ? 0 : newStatus === 'inactive' ? 1 : 2
      ElMessage.success(`${statusText}成功`)
    } else {
      ElMessage.error(response.message || `${statusText}失败`)
      // 恢复原状态
      user.status = user.status === 'active' ? 'inactive' : 'active'
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
      console.error('状态变化错误:', error)
    }
    // 用户取消操作，恢复原状态
    user.status = user.status === 'active' ? 'inactive' : 'active'
  }
}

// 分页处理
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  fetchUsers()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchUsers()
}

onMounted(() => {
  fetchUsers()
})
</script>

<style scoped>
.user-management {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 4px 0;
}

.page-subtitle {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

.filter-section {
  margin-bottom: 16px;
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.username {
  font-weight: 500;
  color: #303133;
}

.email {
  font-size: 12px;
  color: #909399;
}

.pagination-section {
  margin-top: 24px;
  display: flex;
  justify-content: center;
}

.user-detail {
  padding: 16px 0;
}

.user-stats {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #EBEEF5;
}

.user-stats h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
}

.stat-item {
  text-align: center;
  padding: 16px;
  background: #f5f7fa;
  border-radius: 8px;
}

.stat-number {
  font-size: 20px;
  font-weight: 600;
  color: #409EFF;
  margin-bottom: 4px;
}

.last-order-date {
  font-size: 16px;
  line-height: 1.2;
}

.stat-label {
  font-size: 12px;
  color: #909399;
}

/* 表格行样式 */
:deep(.inactive-row) {
  background-color: #fdf6ec;
}

:deep(.locked-row) {
  background-color: #fef0f0;
}

.no-data {
  text-align: center;
  padding: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .filter-section .el-row {
    margin: 0;
  }

  .filter-section .el-col {
    margin-bottom: 12px;
  }

  .user-stats .el-row {
    margin: 0;
  }

  .user-stats .el-col {
    margin-bottom: 12px;
  }
}
</style> 
<template>
    <div class="profile-container">
      <!-- 个人中心导航 -->
      <div class="profile-nav">
        <div class="nav-tabs">
          <div
              v-for="tab in tabs"
              :key="tab.key"
              class="nav-tab"
              :class="{ active: activeTab === tab.key }"
              @click="activeTab = tab.key"
          >
            <el-icon>
              <component :is="tab.icon"/>
            </el-icon>
            <span>{{ tab.name }}</span>
          </div>
        </div>
      </div>
  
      <!-- 基本信息 -->
      <div v-if="activeTab === 'basic'" class="tab-content">
        <div class="content-header">
          <h2>基本信息</h2>
          <p>管理您的个人资料信息</p>
        </div>
  
        <div class="profile-grid">
          <div class="profile-card">
            <div class="avatar-section">
              <div class="avatar-container">
                <el-avatar 
                  :src="userInfo.avatarUrl || '/default-avatar.png'" 
                  :size="120"
                  class="avatar"
                />
                <div class="avatar-overlay" @click="triggerAvatarUpload">
                  <el-icon size="24">
                    <Camera/>
                  </el-icon>
                </div>
              </div>
              <input
                  ref="avatarInput"
                  type="file"
                  accept="image/*"
                  style="display: none"
                  @change="handleAvatarChange"
              />
              <p class="avatar-tip">点击头像更换</p>
            </div>
          </div>
  
          <div class="profile-card">
            <h3>个人信息</h3>
            <el-form :model="userInfo" label-width="80px">
              <el-form-item label="用户名">
                <el-input v-model="userInfo.username" placeholder="请输入用户名"/>
              </el-form-item>
              <el-form-item label="账号">
                <el-input v-model="userInfo.userAccount" disabled/>
              </el-form-item>
              <el-form-item label="邮箱">
                <el-input v-model="userInfo.email" type="email" placeholder="请输入邮箱"/>
              </el-form-item>
              <el-form-item label="手机号">
                <el-input v-model="userInfo.phone" placeholder="请输入手机号"/>
              </el-form-item>
              <el-form-item label="性别">
                <el-select v-model="userInfo.gender" placeholder="请选择性别">
                  <el-option label="未知" value="0"/>
                  <el-option label="男" value="1"/>
                  <el-option label="女" value="2"/>
                </el-select>
              </el-form-item>
              <el-form-item label="用户状态">
                <el-tag :type="getStatusType(userInfo.userStatus)">
                  {{ getStatusText(userInfo.userStatus) }}
                </el-tag>
              </el-form-item>
            </el-form>
          </div>
  
          <div class="profile-card">
            <h3>账户信息</h3>
            <div class="info-list">
              <div class="info-item">
                <span class="label">用户ID</span>
                <span class="value">{{ userInfo.id }}</span>
              </div>
              <div class="info-item">
                <span class="label">用户角色</span>
                <span class="value">{{ getRoleText(userInfo.userRole) }}</span>
              </div>
              <div class="info-item">
                <span class="label">注册时间</span>
                <span class="value">{{ userInfo.createTime }}</span>
              </div>
              <div class="info-item">
                <span class="label">最后登录</span>
                <span class="value">{{ userInfo.lastLoginTime || '从未登录' }}</span>
              </div>
              <div class="info-item">
                <span class="label">最后更新</span>
                <span class="value">{{ userInfo.updateTime || '从未更新' }}</span>
              </div>
            </div>
          </div>
        </div>
  
        <div class="action-buttons">
          <el-button type="primary" @click="saveProfile">保存修改</el-button>
          <el-button @click="resetProfile">重置</el-button>
        </div>
      </div>
  
      <!-- 修改密码 -->
      <div v-if="activeTab === 'password'" class="tab-content">
        <div class="content-header">
          <h2>修改密码</h2>
          <p>定期更换密码以确保账户安全</p>
        </div>
  
        <div class="password-form">
          <el-form :model="passwordForm" label-width="100px">
            <el-form-item label="当前密码">
              <el-input 
                v-model="passwordForm.oldPassword" 
                type="password" 
                placeholder="请输入当前密码"
                show-password
              />
            </el-form-item>
            <el-form-item label="新密码">
              <el-input 
                v-model="passwordForm.newPassword" 
                type="password" 
                placeholder="请输入新密码"
                show-password
              />
            </el-form-item>
            <el-form-item label="确认新密码">
              <el-input 
                v-model="passwordForm.confirmPassword" 
                type="password" 
                placeholder="请再次输入新密码"
                show-password
              />
            </el-form-item>
          </el-form>
  
          <div class="password-strength" v-if="passwordForm.newPassword">
            <h4>密码强度</h4>
            <el-progress 
              :percentage="passwordStrength.percentage" 
              :color="getProgressColor(passwordStrength.percentage)"
              :stroke-width="8"
            />
            <p class="strength-text">{{ passwordStrength.text }}</p>
          </div>
  
          <div class="password-tips">
            <h4>密码要求</h4>
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="tip-item" :class="{ met: passwordForm.newPassword.length >= 8 }">
                  <el-icon><Check v-if="passwordForm.newPassword.length >= 8"/><Close v-else/></el-icon>
                  <span>至少8个字符</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="tip-item" :class="{ met: /[A-Z]/.test(passwordForm.newPassword) }">
                  <el-icon><Check v-if="/[A-Z]/.test(passwordForm.newPassword)"/><Close v-else/></el-icon>
                  <span>包含大写字母</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="tip-item" :class="{ met: /[a-z]/.test(passwordForm.newPassword) }">
                  <el-icon><Check v-if="/[a-z]/.test(passwordForm.newPassword)"/><Close v-else/></el-icon>
                  <span>包含小写字母</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="tip-item" :class="{ met: /\d/.test(passwordForm.newPassword) }">
                  <el-icon><Check v-if="/\d/.test(passwordForm.newPassword)"/><Close v-else/></el-icon>
                  <span>包含数字</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="tip-item" :class="{ met: /[!@#$%^&*]/.test(passwordForm.newPassword) }">
                  <el-icon><Check v-if="/[!@#$%^&*]/.test(passwordForm.newPassword)"/><Close v-else/></el-icon>
                  <span>包含特殊字符</span>
                </div>
              </el-col>
            </el-row>
          </div>
  
          <div class="action-buttons">
            <el-button type="primary" @click="changePasswordClick">修改密码</el-button>
          </div>
        </div>
      </div>
  
      <!-- 操作日志 -->
      <div v-if="activeTab === 'logs'" class="tab-content">
        <div class="content-header">
          <h2>操作日志</h2>
          <p>查看您的操作记录</p>
        </div>
  
        <div class="logs-filters">
          <el-row :gutter="16">
            <el-col :span="6">
              <el-form-item label="操作类型">
                <el-select v-model="logFilters.action" placeholder="全部" clearable>
                  <el-option label="登录" value="login"/>
                  <el-option label="登出" value="logout"/>
                  <el-option label="修改信息" value="update"/>
                  <el-option label="删除操作" value="delete"/>
                  <el-option label="添加操作" value="add"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="时间范围">
                <el-select v-model="logFilters.timeRange">
                  <el-option label="最近1天" value="1"/>
                  <el-option label="最近7天" value="7"/>
                  <el-option label="最近30天" value="30"/>
                  <el-option label="最近90天" value="90"/>
                  <el-option label="最近一年" value="365"/>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-button type="primary" @click="loadUserLogs">查询</el-button>
            </el-col>
          </el-row>
        </div>
  
        <div class="logs-info">
          <span class="logs-count">共 {{ userLogs.length }} 条记录</span>
        </div>
        
        <div class="logs-table">
          <el-table :data="userLogs" style="width: 100%" max-height="400">
            <el-table-column label="时间" width="180">
              <template #default="{ row }">
                {{ formatDateTime(row.createTime) }}
              </template>
            </el-table-column>
            <el-table-column prop="userId" label="用户ID" width="80"/>
            <el-table-column prop="action" label="操作类型" width="100">
              <template #default="{ row }">
                <el-tag :type="getActionType(row.action)" size="small">
                  {{ getActionText(row.action) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="description" label="操作详情" min-width="150"/>
            <el-table-column prop="ipAddress" label="IP地址" width="120"/>
            <el-table-column prop="userAgent" label="设备信息" min-width="200" show-overflow-tooltip/>
          </el-table>
        </div>
      </div>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref, computed, onMounted } from 'vue'
  import { ElMessage } from 'element-plus'
  import { 
    User, 
    Lock, 
    Document, 
    Camera, 
    Check, 
    Close 
  } from '@element-plus/icons-vue'
  import { getUserInfo } from '@/utils/auth'
  import {
    queryUserLogs,
    changePassword,
    updateUser,
    type QueryUserLogsParams,
    type UpdateUserParams
  } from '@/services/userApi'
  import {uploadFile} from "@/services/file.ts";
  
  // 导航标签
  const tabs = [
    { key: 'basic', name: '基本信息', icon: 'User' },
    { key: 'password', name: '修改密码', icon: 'Lock' },
    { key: 'logs', name: '操作日志', icon: 'Document' },
  ]
  
  const activeTab = ref('basic')
  
  // 用户信息
  const userInfo = ref({
    id: 0,
    username: '',
    userAccount: '',
    email: '',
    phone: '',
    gender: 0,
    userStatus: 0,
    userRole: 0,
    avatarUrl: '',
    createTime: '',
    lastLoginTime: '',
    updateTime: ''
  })
  
  // 密码表单
  const passwordForm = ref({
    oldPassword: '',
    newPassword: '',
    confirmPassword: ''
  })
  
  // 日志筛选
  const logFilters = ref({
    action: '',
    timeRange: '30'
  })
  
  interface UserLog {
    id: number
    userId: number
    createTime: Record<string, unknown>
    action: string
    description: string
    ipAddress: string
    userAgent: string
    updateTime: Record<string, unknown>
  }
  
  // 用户日志
  const userLogs = ref<UserLog[]>([])
  
  // 头像上传
  const avatarInput = ref<HTMLInputElement | null>(null)
  
  // 计算属性
  const passwordStrength = computed(() => {
    const password = passwordForm.value.newPassword
    if (!password) return { percentage: 0, text: '' }
  
    let score = 0
    let feedback = []
  
    if (password.length >= 8) score += 20
    if (/[A-Z]/.test(password)) score += 20
    if (/[a-z]/.test(password)) score += 20
    if (/\d/.test(password)) score += 20
    if (/[!@#$%^&*]/.test(password)) score += 20
  
    let text = ''
  
    if (score >= 80) {
      text = '密码强度：强'
    } else if (score >= 60) {
      text = '密码强度：中等'
    } else if (score >= 40) {
      text = '密码强度：弱'
    } else {
      text = '密码强度：很弱'
    }
  
    return { percentage: score, text }
  })
  
  // 方法
  const loadUserInfo = async () => {
    try {
      const userFromStorage = getUserInfo()
      if (userFromStorage) {
        userInfo.value = {
          id: userFromStorage.id,
          username: userFromStorage.username,
          userAccount: userFromStorage.userAccount,
          email: userFromStorage.email,
          phone: userFromStorage.phone,
          gender: userFromStorage.gender,
          userStatus: userFromStorage.userStatus,
          userRole: userFromStorage.userRole,
          avatarUrl: userFromStorage.avatarUrl,
          createTime: String(userFromStorage.createTime),
          lastLoginTime: String(userFromStorage.lastLoginTime),
          updateTime: String(userFromStorage.updateTime)
        }
      }
    } catch (error) {
      ElMessage.error('获取用户信息失败')
    }
  }
  
  const saveProfile = async () => {
    try {
      const userFromStorage = getUserInfo()
      if (!userFromStorage) {
        ElMessage.error('用户信息不存在')
        return
      }
      
      const updateParams = {
        id: userFromStorage.id,
        username: userInfo.value.username,
        email: userInfo.value.email,
        phone: userInfo.value.phone,
        gender: userInfo.value.gender
      }
      
      const response = await updateUser(updateParams)
      if (response.code === 0) {
        ElMessage.success('个人信息保存成功')
        // 更新本地存储的用户信息
        const updatedUserInfo = { ...userFromStorage, ...updateParams }
        sessionStorage.setItem('userinfo', JSON.stringify(updatedUserInfo))
      } else {
        ElMessage.error(response.description || '个人信息保存失败')
      }
    } catch (error) {
      ElMessage.error('个人信息保存失败')
      console.error('保存个人信息错误:', error)
    } finally {
      await loadUserInfo()
    }
  }
  
  const resetProfile = () => {
    loadUserInfo()
    ElMessage.info('已重置为原始信息')
  }
  
  const changePasswordClick = async () => {
    try {
      const userFromStorage = getUserInfo()
      if (!userFromStorage) {
        ElMessage.error('用户信息不存在')
        return
      }
      
      // 验证密码
      if (passwordForm.value.newPassword !== passwordForm.value.confirmPassword) {
        ElMessage.error('两次输入的密码不一致')
        return
      }
      
      if (passwordForm.value.newPassword.length < 8) {
        ElMessage.error('新密码长度不能少于8位')
        return
      }
      
      const changePasswordParams = {
        id: userFromStorage.id,
        oldPassword: passwordForm.value.oldPassword,
        newPassword: passwordForm.value.newPassword,
        confirmPassword: passwordForm.value.confirmPassword
      }
      
      const response = await changePassword(changePasswordParams)
      if (response.code === 0) {
        ElMessage.success('密码修改成功')
        passwordForm.value = {
          oldPassword: '',
          newPassword: '',
          confirmPassword: ''
        }
      } else {
        ElMessage.error(response.description || '密码修改失败')
      }
    } catch (error) {
      ElMessage.error('密码修改失败')
      console.error('修改密码错误:', error)
    }
  }
  
  const loadUserLogs = async () => {
    try {
      const userFromStorage = getUserInfo()
      if (!userFromStorage) {
        ElMessage.error('用户信息不存在')
        return
      }
      
      const params: QueryUserLogsParams = {
        action: logFilters.value.action || undefined,
        timeRange: Number(logFilters.value.timeRange)
      }
      
      const response = await queryUserLogs(params)
      if (response.code === 0) {
        userLogs.value = response.data
      } else {
        ElMessage.error(response.description || '日志加载失败')
        userLogs.value = []
      }
    } catch (error) {
      ElMessage.error('日志加载失败')
      console.error('加载用户日志错误:', error)
      userLogs.value = []
    }
  }
  
  const triggerAvatarUpload = () => {
    avatarInput.value?.click()
  }
  
    const handleAvatarChange = async (event: Event) => {
    const file = (event.target as HTMLInputElement).files?.[0]
    if (!file) return

    try {
      // 验证文件类型
      if (!file.type.startsWith('image/')) {
        ElMessage.error('请选择图片文件')
        return
      }

      // 验证文件大小（限制为5MB）
      if (file.size > 5 * 1024 * 1024) {
        ElMessage.error('图片大小不能超过5MB')
        return
      }

      const response = await uploadFile(file)
      if (response.code === 0) {
        // 更新用户头像
        const userFromStorage = getUserInfo()
        if (userFromStorage) {
          const updateParams: UpdateUserParams = {
            id: userFromStorage.id,
            avatarUrl: response.data
          }
          
          const updateResponse = await updateUser(updateParams)
          if (updateResponse.code === 0) {
            // 更新本地存储和显示
            userInfo.value.avatarUrl = response.data
            const updatedUserInfo = { ...userFromStorage, avatarUrl: response.data }
            sessionStorage.setItem('userinfo', JSON.stringify(updatedUserInfo))
            ElMessage.success('头像上传成功')
          } else {
            ElMessage.error(updateResponse.description || '头像更新失败')
          }
        }
      } else {
        ElMessage.error(response.description || '头像上传失败')
      }
    } catch (error) {
      ElMessage.error('头像上传失败')
      console.error('头像上传错误:', error)
    }
  }
  
  const getStatusType = (status: number) => {
    switch (status) {
      case 0: return 'success'
      case 1: return 'warning'
      case 2: return 'danger'
      default: return 'info'
    }
  }
  
  const getStatusText = (status: number) => {
    switch (status) {
      case 0: return '正常'
      case 1: return '禁用'
      case 2: return '已锁定'
      default: return '未知'
    }
  }
  
  const getRoleText = (role: number) => {
    switch (role) {
      case 0: return '普通用户'
      case 1: return '管理员'
      case 2: return '超级管理员'
      default: return '未知'
    }
  }
  
  const getActionText = (action: string) => {
    const actionMap: Record<string, string> = {
      login: '登录',
      logout: '登出',
      update: '修改',
      delete: '删除',
      add: '添加'
    }
    return actionMap[action] || action
  }
  
  const getActionType = (action: string) => {
    const typeMap: Record<string, string> = {
      login: 'success',
      logout: 'info',
      update: 'warning',
      delete: 'danger',
      add: 'primary'
    }
    return typeMap[action] || 'info'
  }
  
  const getProgressColor = (percentage: number) => {
    if (percentage >= 80) return '#67C23A'
    if (percentage >= 60) return '#E6A23C'
    if (percentage >= 40) return '#F56C6C'
    return '#F56C6C'
  }
  
  // 格式化日期时间
  const formatDateTime = (dateTime: Record<string, unknown>) => {
    if (!dateTime) return ''
    try {
      const date = new Date(String(dateTime))
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    } catch (error) {
      return String(dateTime)
    }
  }
  
  onMounted(() => {
    loadUserInfo()
    loadUserLogs()
  })
  </script>
  
  <style scoped>
  .profile-container {
    background: white;
    border-radius: 8px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  /* 导航样式 */
  .profile-nav {
    margin-bottom: 24px;
  }
  
  .nav-tabs {
    display: flex;
    gap: 0;
    border-bottom: 1px solid #E4E7ED;
  }
  
  .nav-tab {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    color: #606266;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
    background: none;
    border: none;
    font-size: 14px;
  }
  
  .nav-tab:hover {
    color: #409EFF;
  }
  
  .nav-tab.active {
    color: #409EFF;
    border-bottom-color: #409EFF;
  }
  
  .nav-tab .el-icon {
    font-size: 16px;
  }
  
  /* 内容区域 */
  .tab-content {
    background: white;
    border-radius: 8px;
  }
  
  .content-header {
    margin-bottom: 24px;
  }
  
  .content-header h2 {
    color: #303133;
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 8px;
  }
  
  .content-header p {
    color: #909399;
    font-size: 14px;
    margin: 0;
  }
  
  /* 个人信息网格 */
  .profile-grid {
    display: grid;
    grid-template-columns: 1fr 2fr 1fr;
    gap: 24px;
    margin-bottom: 24px;
  }
  
  .profile-card {
    background: #FAFAFA;
    border: 1px solid #E4E7ED;
    border-radius: 8px;
    padding: 20px;
  }
  
  .profile-card h3 {
    color: #303133;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 16px;
  }
  
  /* 头像区域 */
  .avatar-section {
    text-align: center;
  }
  
  .avatar-container {
    position: relative;
    display: inline-block;
    margin-bottom: 16px;
  }
  
  .avatar {
    border: 3px solid #E4E7ED;
  }
  
  .avatar-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    cursor: pointer;
  }
  
  .avatar-overlay:hover {
    opacity: 1;
  }
  
  .avatar-overlay .el-icon {
    color: white;
  }
  
  .avatar-tip {
    color: #909399;
    font-size: 12px;
    margin: 0;
  }
  
  /* 信息列表 */
  .info-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }
  
  .info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #F0F0F0;
  }
  
  .info-item:last-child {
    border-bottom: none;
  }
  
  .info-item .label {
    color: #606266;
    font-size: 14px;
  }
  
  .info-item .value {
    color: #303133;
    font-size: 14px;
    font-weight: 500;
  }
  
  /* 密码表单 */
  .password-form {
    max-width: 500px;
  }
  
  .password-strength {
    margin: 20px 0;
    padding: 16px;
    background: #F8F9FA;
    border-radius: 8px;
  }
  
  .password-strength h4 {
    color: #303133;
    font-size: 14px;
    margin-bottom: 12px;
  }
  
  .strength-text {
    color: #606266;
    font-size: 12px;
    margin: 8px 0 0 0;
  }
  
  .password-tips {
    margin: 20px 0;
    padding: 16px;
    background: #F8F9FA;
    border-radius: 8px;
  }
  
  .password-tips h4 {
    color: #303133;
    font-size: 14px;
    margin-bottom: 12px;
  }
  
  .tip-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    color: #909399;
    font-size: 12px;
  }
  
  .tip-item.met {
    color: #67C23A;
  }
  
  .tip-item .el-icon {
    font-size: 14px;
  }
  
  /* 日志管理 */
  .logs-filters {
    margin-bottom: 20px;
  }
  
  .logs-info {
    margin-bottom: 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .logs-count {
    color: #606266;
    font-size: 12px;
    font-weight: 500;
  }
  
  .logs-table {
    background: white;
    border-radius: 8px;
    overflow: hidden;
  }
  
  /* 操作按钮 */
  .action-buttons {
    display: flex;
    gap: 12px;
    margin-top: 24px;
  }
  
  /* 响应式设计 */
  @media (max-width: 768px) {
    .profile-grid {
      grid-template-columns: 1fr;
      gap: 16px;
    }
    
    .nav-tabs {
      flex-wrap: wrap;
    }
    
    .nav-tab {
      padding: 8px 16px;
      font-size: 12px;
    }
  }
  </style>
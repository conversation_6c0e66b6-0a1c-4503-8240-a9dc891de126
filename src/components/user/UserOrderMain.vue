<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  queryOrdersPage,
  updateOrders,
  type QueryOrdersPageParams,
  payOrders,
  cancelOrders
} from '../../services/ordersApi'
import { transformOrderData, ORDER_STATUS_TEXT } from '../../config/api'
import { getUserInfo } from '../../utils/auth'
import PaymentModal from '../order/PaymentModal.vue'

const router = useRouter()

// 订单接口类型定义
interface Order {
  id: number
  carId: number
  carName: string
  userId: number
  orderNumber: string
  startDate: string
  endDate: string
  totalDays: number
  dailyRate: number
  totalAmount: number
  status: string
  statusText: string
  paymentMethod?: string
  paymentTime?: string | null
  pickupLocation?: string
  returnLocation?: string
  contactPhone: string
  remark?: string
  createdAt: string
  updatedAt: string
  isDelete: number
}

// 订单状态映射
const ORDER_STATUS_MAP = {
  pending: { type: 'warning', text: '待支付' },
  paid: { type: 'info', text: '已支付' },
  completed: { type: 'success', text: '已完成' },
  cancelled: { type: 'danger', text: '已取消' }
} as const

// 响应式数据
const orders = ref<Order[]>([])
const loading = ref(true)
const statusFilter = ref('')
const payingOrderId = ref<number | null>(null)
const cancellingOrderId = ref<number | null>(null)
const showPaymentModal = ref(false)
const currentPaymentOrder = ref<Order | null>(null)

// 分页数据
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0
})

// 计算属性
const filteredOrders = computed(() => {
  if (!statusFilter.value) {
    return orders.value
  }
  return orders.value.filter(order => order.status === statusFilter.value)
})

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 获取当前用户ID
const getCurrentUserId = (): number => {
  const userInfo = getUserInfo()
  return userInfo?.id || 1 // 默认用户ID为1，实际应该从用户信息中获取
}

// 获取订单列表
const fetchOrders = async () => {
  loading.value = true
  try {
    const userId = getCurrentUserId()
    const params: QueryOrdersPageParams = {
      pageNum: pagination.value.current,
      pageSize: pagination.value.pageSize,
      params: {
        userId: userId,
      },
      sort: {
        column: 'createdAt',
        asc: false // 按创建时间倒序
      }
    }

    const response = await queryOrdersPage(params)
    
    if (response.code === 0 && response.data) {
      // 转换订单数据
      orders.value = response.data.list.map(transformOrderData)
      pagination.value.total = response.data.total
    } else {
      ElMessage.error(response.description || '获取订单列表失败')
      orders.value = []
    }
  } catch (error) {
    ElMessage.error('获取订单列表失败')
    console.error('获取订单列表错误:', error)
    orders.value = []
  } finally {
    loading.value = false
  }
}

// 分页变化处理
const handleCurrentChange = (page: number) => {
  pagination.value.current = page
  fetchOrders()
}

const handleSizeChange = (size: number) => {
  pagination.value.pageSize = size
  pagination.value.current = 1
  fetchOrders()
}

// 查看订单详情
const viewOrderDetail = (orderId: number) => {
  router.push(`/order/${orderId}`)
}

// 支付订单
const handlePayOrder = (order: Order) => {
  currentPaymentOrder.value = order
  showPaymentModal.value = true
}

// 处理支付成功
const handlePaymentSuccess = async (orderId: number, paymentInfo: any) => {
  try {
    payingOrderId.value = orderId
    
    // 创建符合后端格式的日期字符串
    const now = new Date()
    const year = now.getFullYear()
    const month = String(now.getMonth() + 1).padStart(2, '0')
    const day = String(now.getDate()).padStart(2, '0')
    const hours = String(now.getHours()).padStart(2, '0')
    const minutes = String(now.getMinutes()).padStart(2, '0')
    const seconds = String(now.getSeconds()).padStart(2, '0')
    const formattedDate = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    
    // 调用真实的订单更新API
    const response = await payOrders({
      id: orderId,
      carId: currentPaymentOrder.value?.carId || 0,
      status: 'paid',
      paymentTime: formattedDate,
      paymentMethod: paymentInfo.methodName
    })
    
    if (response.code === 0) {
      ElMessage.success('支付成功！')
      // 刷新订单列表
      await fetchOrders()
      // 关闭支付弹窗
      showPaymentModal.value = false
      currentPaymentOrder.value = null
    } else {
      ElMessage.error(response.description || '支付失败')
    }
  } catch (error) {
    ElMessage.error('支付失败，请重试')
    console.error('支付订单错误:', error)
  } finally {
    payingOrderId.value = null
  }
}

// 处理支付取消
const handlePaymentCancel = () => {
  ElMessage.info('支付已取消')
  showPaymentModal.value = false
  currentPaymentOrder.value = null
}

// 取消订单
const handleCancelOrder = async (orderId: number) => {
  try {
    await ElMessageBox.confirm(
      '确定要取消这个订单吗？',
      '确认取消',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    cancellingOrderId.value = orderId
    
    // 调用真实的订单更新API
    const response = await cancelOrders({
      id: orderId,
      status: 'cancelled'
    })
    
    if (response.code === 0) {
      ElMessage.success('订单已取消')
      // 刷新订单列表
      await fetchOrders()
    } else {
      ElMessage.error(response.description || '取消订单失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('取消订单失败，请重试')
      console.error('取消订单错误:', error)
    }
  } finally {
    cancellingOrderId.value = null
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchOrders()
})
</script>

<template>
  <div class="container">
    <div class="header">
      <h2>我的订单</h2>
      <el-select
        v-model="statusFilter"
        placeholder="筛选状态"
        clearable
        style="width: 150px"
      >
        <el-option label="待支付" value="pending" />
        <el-option label="已支付" value="paid" />
        <el-option label="已完成" value="completed" />
        <el-option label="已取消" value="cancelled" />
      </el-select>
    </div>

    <div v-loading="loading">
      <div v-if="filteredOrders.length === 0" class="empty-state">
        <el-empty description="暂无订单数据">
          <el-button type="primary" @click="$router.push('/user/home')">
            去租车
          </el-button>
        </el-empty>
      </div>

      <div v-else class="orders-list">
        <el-card v-for="order in filteredOrders" :key="order.id" class="order-item">
          <template #header>
            <div class="order-header">
              <span class="order-number">订单号：{{ order.orderNumber }}</span>
              <el-tag 
                :type="(ORDER_STATUS_MAP[order.status as keyof typeof ORDER_STATUS_MAP] || { type: 'info' }).type as any"
              >
                {{ order.statusText }}
              </el-tag>
            </div>
          </template>

          <div class="order-content">
            <div class="car-info">
              <h4>{{ order.carName }}</h4>
              <p>租赁时间：{{ order.startDate }} 至 {{ order.endDate }}</p>
              <p>租赁天数：{{ order.totalDays }} 天</p>
              <p>日租金：¥{{ order.dailyRate }}</p>
              <p v-if="order.pickupLocation">取车地点：{{ order.pickupLocation }}</p>
              <p v-if="order.returnLocation">还车地点：{{ order.returnLocation }}</p>
            </div>
            <div class="order-meta">
              <div class="amount">
                <span class="label">总金额：</span>
                <span class="price">¥{{ order.totalAmount }}</span>
              </div>
              <div class="date">
                下单时间：{{ formatDate(order.createdAt) }}
              </div>
              <div v-if="order.paymentTime" class="date">
                支付时间：{{ formatDate(order.paymentTime) }}
              </div>
            </div>
          </div>

          <template #footer>
            <div class="order-actions">
              <el-button size="small" @click="viewOrderDetail(order.id)">
                查看详情
              </el-button>
              <el-button
                v-if="order.status === 'pending'"
                type="primary"
                size="small"
                :loading="payingOrderId === order.id"
                @click="handlePayOrder(order)"
              >
                {{ payingOrderId === order.id ? '支付中...' : '立即支付' }}
              </el-button>
              <el-button
                v-if="order.status === 'pending'"
                type="danger"
                size="small"
                :loading="cancellingOrderId === order.id"
                @click="handleCancelOrder(order.id)"
              >
                {{ cancellingOrderId === order.id ? '取消中...' : '取消订单' }}
              </el-button>
            </div>
          </template>
        </el-card>
      </div>

      <!-- 分页组件 -->
      <div v-if="orders.length > 0" class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[5, 10, 20, 50]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 支付弹窗 -->
    <PaymentModal
      v-model="showPaymentModal"
      :order-info="currentPaymentOrder || {
        id: 0,
        orderNumber: '',
        carName: '',
        startDate: '',
        endDate: '',
        totalDays: 0,
        totalAmount: 0
      }"
      @payment-success="handlePaymentSuccess"
      @payment-cancel="handlePaymentCancel"
    />
  </div>
</template>

<style scoped>
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  font-size: 28px;
  color: #303133;
}

.orders-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.order-item {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.order-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-number {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.order-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.car-info {
  flex: 1;
  margin-right: 20px;
}

.car-info h4 {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.car-info p {
  font-size: 14px;
  color: #606266;
  margin-bottom: 4px;
}

.order-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  min-width: 120px;
}

.amount .label {
  font-size: 14px;
  color: #606266;
}

.amount .price {
  font-size: 18px;
  font-weight: 600;
  color: #FF6700;
}

.date {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.order-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.empty-state {
  background: white;
  border-radius: 12px;
  padding: 60px 32px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .header h2 {
    font-size: 24px;
  }

  .order-item {
    padding: 16px;
  }

  .order-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .car-info {
    margin-right: 0;
  }

  .order-meta {
    align-items: flex-start;
    min-width: auto;
  }

  .order-actions {
    flex-direction: column;
  }

  .order-actions .el-button {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 16px;
  }
  
  .header h2 {
    font-size: 22px;
  }
}
</style>
<template>
  <!-- 顶部轮播图 -->
  <section class="hero-section">
    <div class="carousel-container">
      <div class="carousel-stage">
        <!-- 轮播卡片 -->
        <div
            v-for="(banner, index) in banners"
            :key="banner.id"
            class="carousel-card"
            :class="{
            'active': index === currentBannerIndex,
            'prev': index === getPreviousIndex,
            'next': index === getNextIndex,
            'hidden': !isVisible(index)
          }"
            :style="getCardStyle(index)"
            @click="goToIndex(index)"
        >
          <div class="card-content">
            <div class="card-image">
              <img
                  v-if="banner.image"
                  :src="banner.image"
                  :alt="banner.title"
              />
              <div class="card-overlay" :style="{ background: banner.gradient }"></div>
            </div>
            <div class="card-text">
              <h2 class="card-title">{{ banner.title }}</h2>
              <p class="card-subtitle">{{ banner.subtitle }}</p>
              <el-button type="primary" size="large" class="card-btn">
                {{ banner.buttonText || '立即租赁' }}
              </el-button>
            </div>
            <div class="card-icon">
              <el-icon :size="80">
                <Van/>
              </el-icon>
            </div>
          </div>
        </div>
      </div>

      <!-- 导航按钮 -->
      <div class="carousel-nav">
        <div class="nav-dots">
          <div
              v-for="(banner, index) in banners"
              :key="index"
              class="nav-dot"
              :class="{ active: index === currentBannerIndex }"
              @click="goToIndex(index)"
          ></div>
        </div>
      </div>
    </div>
  </section>

  <!-- 汽车展示区 -->
  <section class="cars-section">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">热门车型</h2>
        <p class="section-subtitle">精选优质车型，满足您的出行需求</p>
      </div>

      <!-- 筛选组件 -->
      <CarSearchFilter
          :loading="loading"
          @search="handleSearch"
          @reset="handleReset"
      />

      <!-- 汽车网格 -->
      <div v-if="cars.length > 0" class="cars-grid" v-loading="loading">
        <CarCard
            v-for="car in cars"
            :key="car.id"
            :car="car"
            class="car-item"
        />
      </div>

      <!-- 加载状态 -->
      <div v-else-if="loading" class="loading-state">
        <el-skeleton :rows="6" animated/>
      </div>

      <!-- 分页组件 -->
      <div v-if="cars.length > 0" class="pagination-container">
        <el-pagination
            v-model:current-page="pagination.currentPage"
            v-model:page-size="pagination.pageSize"
            :page-sizes="pagination.pageSizes"
            :total="pagination.total"
            :background="true"
            :disabled="loading"
            layout="total, sizes, prev, pager, next, jumper"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
        />
      </div>

      <!-- 空状态 -->
      <div v-else-if="!loading" class="empty-state">
        <el-empty :description="getEmptyDescription()">
          <el-button type="primary" @click="handleReset" v-if="hasActiveFilters">
            清除筛选条件
          </el-button>
          <el-button type="primary" @click="fetchData" v-else>
            重新加载
          </el-button>
        </el-empty>
      </div>
    </div>
  </section>

  <!-- 悬浮AI聊天 -->
  <FloatAIChat />
</template>

<script setup lang="ts">

import {Van, ArrowLeft, ArrowRight} from "@element-plus/icons-vue";
import CarCard from "@/components/car/CarCard.vue";
import CarSearchFilter from "@/components/car/CarSearchFilter.vue";
import FloatAIChat from "@/components/ai/FloatAIChat.vue";
import {computed, onMounted, onUnmounted, ref} from "vue";
import {queryCarPage, type QueryCarPageParams} from "@/services/carApi.ts";
import {CAR_STATUS, transformCarData} from "@/config/api.ts";
import {type Banner, bannerApi} from "@/services/bannerApi.ts";
import {ElMessage} from "element-plus";

// 定义Car类型（从数据库返回的数据结构）
interface Car {
  id: number
  name: string
  engine: string
  seats: number
  transmission: string
  dailyRate: number
  brand: string
  year: number
  mileage: number
  fuelType: string
  available: boolean
  image: string
  status: string
}

// 响应式数据
const banners = ref<Banner[]>([])
const cars = ref<Car[]>([])
const loading = ref(false)
const currentBannerIndex = ref(0)
const carouselRef = ref()

// 分页数据
const pagination = ref({
  currentPage: 1,
  pageSize: 9,
  total: 0,
  pageSizes: [6, 9, 12, 18, 24]
})

// 筛选条件数据
const searchFilters = ref({
  name: '',
  brand: '',
  seats: undefined as number | undefined,
  transmission: '',
  fuelType: '',
  dailyRateMin: undefined as number | undefined,
  dailyRateMax: undefined as number | undefined,
  year: undefined as number | undefined
})

// 获取查询参数
const getQueryParams = (): QueryCarPageParams => {
  const params: any = {
    available: CAR_STATUS.AVAILABLE // 只获取可用的车辆
  }

  // 添加筛选条件
  if (searchFilters.value.name?.trim()) {
    params.name = searchFilters.value.name.trim()
  }
  if (searchFilters.value.brand) {
    params.brand = searchFilters.value.brand
  }
  if (searchFilters.value.seats) {
    params.seats = searchFilters.value.seats
  }
  if (searchFilters.value.transmission) {
    params.transmission = searchFilters.value.transmission
  }
  if (searchFilters.value.fuelType) {
    params.fuelType = searchFilters.value.fuelType
  }
  if (searchFilters.value.dailyRateMin !== undefined && searchFilters.value.dailyRateMin >= 0) {
    params.dailyRateMin = searchFilters.value.dailyRateMin
  }
  if (searchFilters.value.dailyRateMax !== undefined && searchFilters.value.dailyRateMax >= 0) {
    params.dailyRateMax = searchFilters.value.dailyRateMax
  }
  if (searchFilters.value.year) {
    params.year = searchFilters.value.year
  }

  return {
    pageNum: pagination.value.currentPage,
    pageSize: pagination.value.pageSize,
    sort: {
      column: 'dailyRate',
      asc: true
    },
    params
  }
}

// API请求函数
const fetchData = async () => {
  loading.value = true

  try {
    // 使用真实数据库API
    const [bannersResult, carsResponse] = await Promise.all([
      bannerApi.getBanners(),
      queryCarPage(getQueryParams())
    ])

    banners.value = bannersResult

    // 处理数据库返回的车辆数据
    if (carsResponse.code === 0 && carsResponse.data?.list) {
      cars.value = carsResponse.data.list.map(transformCarData)
      pagination.value.total = carsResponse.data.total

      // 判断是否有筛选条件
      const hasFilters = Object.values(searchFilters.value).some(value =>
          value !== '' && value !== undefined && value !== null
      )

      if (hasFilters) {
        ElMessage.success(`筛选结果：找到 ${pagination.value.total} 辆匹配的车辆`)
      } else {
        ElMessage.success(`加载了 ${cars.value.length} 辆车辆，共 ${pagination.value.total} 辆`)
      }
    } else {
      cars.value = []
      pagination.value.total = 0
      ElMessage.warning(carsResponse.description || '暂无车辆数据')
    }

  } catch (error) {
    ElMessage.error('数据加载失败')
    console.error('数据加载错误:', error)
    banners.value = []
    cars.value = []
  } finally {
    loading.value = false
  }
}

// 分页事件处理
const handleCurrentChange = (page: number) => {
  pagination.value.currentPage = page
  fetchData()
}

const handleSizeChange = (size: number) => {
  pagination.value.pageSize = size
  pagination.value.currentPage = 1 // 重置到第一页
  fetchData()
}

// 筛选事件处理
const handleSearch = (filters: any) => {
  // 重置所有筛选条件，然后应用新的筛选条件
  searchFilters.value = {
    name: '',
    brand: '',
    seats: undefined,
    transmission: '',
    fuelType: '',
    dailyRateMin: undefined,
    dailyRateMax: undefined,
    year: undefined
  }
  // 应用新的筛选条件
  Object.assign(searchFilters.value, filters)
  // 重置到第一页
  pagination.value.currentPage = 1
  // 重新获取数据
  fetchData()
}

const handleReset = () => {
  // 重置筛选条件
  searchFilters.value = {
    name: '',
    brand: '',
    seats: undefined,
    transmission: '',
    fuelType: '',
    dailyRateMin: undefined,
    dailyRateMax: undefined,
    year: undefined
  }
  // 重置到第一页
  pagination.value.currentPage = 1
  // 重新获取数据
  fetchData()
}

// 计算属性
const hasActiveFilters = computed(() => {
  return Object.values(searchFilters.value).some(value =>
      value !== '' && value !== undefined && value !== null
  )
})

// 轮播图相关计算属性
const getPreviousIndex = computed(() => {
  if (banners.value.length === 0) return -1
  return currentBannerIndex.value === 0 ? banners.value.length - 1 : currentBannerIndex.value - 1
})

const getNextIndex = computed(() => {
  if (banners.value.length === 0) return -1
  return currentBannerIndex.value === banners.value.length - 1 ? 0 : currentBannerIndex.value + 1
})

const isVisible = (index: number) => {
  const total = banners.value.length
  if (total === 0) return false

  const current = currentBannerIndex.value
  const prev = current === 0 ? total - 1 : current - 1
  const next = current === total - 1 ? 0 : current + 1

  return index === current || index === prev || index === next
}

const getCardStyle = (index: number) => {
  const total = banners.value.length
  if (total === 0) return {}

  const current = currentBannerIndex.value
  const prev = current === 0 ? total - 1 : current - 1
  const next = current === total - 1 ? 0 : current + 1

  if (index === current) {
    return {
      transform: 'translateX(0) scale(1.1)',
      zIndex: 3
    }
  } else if (index === prev) {
    return {
      transform: 'translateX(-65%) scale(0.75)',
      zIndex: 1,
      filter: 'brightness(0.6)'
    }
  } else if (index === next) {
    return {
      transform: 'translateX(65%) scale(0.75)',
      zIndex: 1,
      filter: 'brightness(0.6)'
    }
  } else {
    return {
      transform: 'translateX(0) scale(0.6)',
      zIndex: 0,
      opacity: 0
    }
  }
}

const getEmptyDescription = () => {
  return hasActiveFilters.value
      ? '当前筛选条件下暂无匹配的车辆，请尝试调整筛选条件'
      : '暂无车辆数据'
}

// 轮播图相关方法
const goToIndex = (index: number) => {
  currentBannerIndex.value = index
}

// 自动播放
let autoPlayTimer: number | null = null

const startAutoPlay = () => {
  autoPlayTimer = window.setInterval(() => {
    const nextIndex = currentBannerIndex.value === banners.value.length - 1 ? 0 : currentBannerIndex.value + 1
    currentBannerIndex.value = nextIndex
  }, 4000)
}

const stopAutoPlay = () => {
  if (autoPlayTimer) {
    clearInterval(autoPlayTimer)
    autoPlayTimer = null
  }
}
onMounted(() => {
  fetchData()
  startAutoPlay()
})

onUnmounted(() => {
  stopAutoPlay()
})
</script>

<style scoped>

/* 轮播图样式 */
.hero-section {
  margin-top: 80px;
  margin-bottom: 40px;
}

.carousel-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  height: 600px;
}

.carousel-stage {
  position: relative;
  width: 100%;
  height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;
  perspective: 1000px;
  max-width: 1200px;
  margin: 0 auto;
}

.carousel-card {
  position: absolute;
  width: 900px;
  height: 500px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  cursor: pointer;
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.carousel-card.hidden {
  opacity: 0;
  pointer-events: none;
}

.carousel-card.active {
  z-index: 3;
}

.carousel-card.prev,
.carousel-card.next {
  z-index: 1;
  opacity: 0.8;
}

.preview-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  background: rgba(255, 255, 255, 0.15);
}

.card-content {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 80px;
  color: white;
  position: relative;
}

.card-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.carousel-card:hover .card-image img {
  transform: scale(1.05);
}

.card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.2;
}

.card-text {
  flex: 1;
  max-width: 500px;
  position: relative;
  z-index: 2;
}

.card-title {
  font-family: 'Poppins', sans-serif;
  font-size: 36px;
  font-weight: 700;
  margin: 0 0 16px 0;
  line-height: 1.2;
}

.card-subtitle {
  font-size: 18px;
  margin: 0 0 24px 0;
  opacity: 0.9;
  line-height: 1.5;
}

.card-btn {
  height: 48px;
  padding: 0 32px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 8px;
}

.card-icon {
  opacity: 0.8;
  position: relative;
  z-index: 2;
}

.carousel-nav {
  margin-top: 30px;
}

.nav-dots {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.nav-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-dot:hover {
  background: rgba(255, 255, 255, 0.5);
  transform: scale(1.2);
}

.nav-dot.active {
  background: #409EFF;
  transform: scale(1.3);
}


/* 汽车展示区样式 */
.cars-section {
  padding: 40px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.section-header {
  text-align: center;
  margin-bottom: 40px;
}

.section-title {
  font-family: 'Poppins', sans-serif;
  font-size: 32px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 12px 0;
}

.section-subtitle {
  font-size: 16px;
  color: #909399;
  margin: 0;
}

.cars-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-top: 32px;
  justify-items: start;
}

.car-item {
  width: 100%;
  max-width: 380px;
}

.empty-state {
  padding: 60px 20px;
  text-align: center;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 40px;
  padding: 20px 0;
}

.loading-state {
  padding: 20px;
  background: white;
  border-radius: 12px;
  margin-top: 32px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .carousel-container {
    height: auto;
  }

  .carousel-stage {
    height: 400px;
  }

  .carousel-card {
    width: 700px;
    height: 400px;
  }

  .card-content {
    padding: 0 60px;
  }

  .card-title {
    font-size: 28px;
  }

  .card-subtitle {
    font-size: 16px;
  }
}

@media (max-width: 768px) {
  .carousel-container {
    padding: 0 10px;
  }

  .carousel-stage {
    height: 300px;
  }

  .carousel-card {
    width: 100%;
    max-width: 400px;
    height: 300px;
  }

  .card-content {
    padding: 0 30px;
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }

  .card-title {
    font-size: 24px;
  }

  .card-subtitle {
    font-size: 14px;
  }

  .card-icon {
    display: none;
  }
}
</style>
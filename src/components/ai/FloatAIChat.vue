<template>
  <div class="floating-ai-chat">
    <!-- 悬浮按钮 -->
    <div
        ref="floatingButton"
        class="floating-button"
        :class="{ 'chat-open': isChatOpen }"
        @click="toggleChat"
        @mousedown="startDrag"
        :style="buttonStyle"
    >
      <el-icon :size="24" v-if="!isChatOpen">
        <ChatDotRound/>
      </el-icon>
      <el-icon :size="24" v-else>
        <Close/>
      </el-icon>
      <span class="button-text" v-if="!isChatOpen">AI助手</span>
    </div>

    <!-- 聊天窗口 -->
    <div
        v-if="isChatOpen"
        class="chat-window"
        :class="{ 'chat-minimized': isMinimized }"
        :style="chatStyle"
    >
      <!-- 聊天窗口头部 -->
      <div class="chat-header" @mousedown="startDrag">
        <div class="header-left">
          <el-icon class="ai-icon">
            <Service/>
          </el-icon>
          <span class="chat-title">智慧租车助手</span>
        </div>
        <div class="header-actions">
          <el-button
              type="text"
              size="small"
              @click="toggleMinimize"
              class="minimize-btn"
          >
            <el-icon>
              <component :is="isMinimized ? 'FullScreen' : 'Minus'"/>
            </el-icon>
          </el-button>
          <el-button
              type="text"
              size="small"
              @click="toggleChat"
              class="close-btn"
          >
            <el-icon>
              <Close/>
            </el-icon>
          </el-button>
        </div>
      </div>

      <!-- 聊天内容区域 -->
      <div class="chat-content" v-show="!isMinimized">
        <div class="messages-container" ref="messagesContainer">
          <!-- 欢迎消息 -->
          <div class="message ai-message">
            <div class="message-avatar">
              <el-avatar :size="32" :icon="Service"/>
            </div>
            <div class="message-content">
              <div class="message-header">
                <span class="message-name">智慧租车助手</span>
                <span class="message-time">{{ formatTime(new Date()) }}</span>
              </div>
              <div class="message-text">
                您好！我是您的智慧租车助手，我可以帮您：
                <ul>
                  <li>根据您的需求推荐合适的车型</li>
                  <li>提供租车价格估算</li>
                  <li>解答租车相关问题</li>
                  <li>协助您完成租车流程</li>
                </ul>
                请告诉我您的租车需求，比如：出行人数、预算范围、使用场景等。
              </div>
            </div>
          </div>

          <!-- 对话消息 -->
          <div
              v-for="(message, index) in chatMessages"
              :key="index"
              :class="['message', message.type === 'user' ? 'user-message' : 'ai-message']"
          >
            <div class="message-avatar">
              <el-avatar
                  :size="32"
                  :src="message.type === 'user' ? userAvatar : ''"
                  :icon="message.type === 'user' ? User : Service"
              />
            </div>
            <div class="message-content">
              <div class="message-header">
                <span class="message-name">{{ message.type === 'user' ? '我' : '智慧租车助手' }}</span>
                <div class="message-actions">
                  <button
                      v-if="message.type === 'ai'"
                      @click="copyMessage(message.content)"
                      class="copy-btn"
                      title="复制消息"
                  >
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                      <path
                          d="M8 4v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V7.242a2 2 0 0 0-.602-1.43L16.083 2.57A2 2 0 0 0 14.685 2H10a2 2 0 0 0-2 2z"/>
                      <path d="M16 18v2a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V9a2 2 0 0 1 2-2h2"/>
                    </svg>
                  </button>
                  <span class="message-time">{{ formatTime(message.time) }}</span>
                </div>
              </div>
              <div class="message-text markdown-content" v-html="formatMessage(message.content)"></div>
              <span v-if="isStreaming && index === chatMessages.length - 1" class="typing-cursor">|</span>
            </div>
          </div>

          <!-- 加载状态 -->
          <div v-if="isLoading && !isStreaming" class="message ai-message">
            <div class="message-avatar">
              <el-avatar :size="32" :icon="Service"/>
            </div>
            <div class="message-content">
              <div class="message-header">
                <span class="message-name">智慧租车助手</span>
              </div>
              <div class="message-text">
                <div class="typing-indicator">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 输入区域 -->
        <div class="input-area">
          <div class="input-container">
            <el-input
                v-model="userInput"
                type="textarea"
                :rows="2"
                placeholder="请输入您的租车需求..."
                @keydown.enter="handleEnterKey"
                :disabled="isLoading || isStreaming"
                resize="none"
            />
            <div class="input-actions">
              <div class="quick-actions">
                <el-button
                    v-for="(action, index) in quickActions"
                    :key="index"
                    size="small"
                    @click="sendQuickMessage(action.text)"
                    :disabled="isLoading || isStreaming"
                >
                  {{ action.label }}
                </el-button>
              </div>
              <el-button
                  type="primary"
                  @click="sendMessage"
                  :loading="isLoading || isStreaming"
                  :disabled="!userInput.trim() || isStreaming"
                  size="small"
              >
                <el-icon>
                  <Promotion/>
                </el-icon>
                发送
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 调整大小手柄 - 固定在聊天窗口右下角 -->
      <div
          v-show="!isMinimized"
          class="resize-handle"
          @mousedown="startResize"
          title="调整大小"
      >
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M22 22L13 13M13 22L22 13"/>
        </svg>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {ref, computed, nextTick, watch, onMounted, onUnmounted} from 'vue'
import {ElMessage} from 'element-plus'
import {
  Service,
  User,
  ChatDotRound,
  Close,
  Promotion,
  Minus,
  FullScreen
} from '@element-plus/icons-vue'
import {chatStream, type ChatStreamParams} from '@/services/ai'
import {getUserInfo} from '@/utils/auth'
import MarkdownIt from 'markdown-it'
import hljs from 'highlight.js'
import 'highlight.js/styles/github.css'

// 响应式数据
const isChatOpen = ref(false)
const isMinimized = ref(false)
const isDragging = ref(false)
const dragOffset = {x: 0, y: 0}
const buttonPosition = ref({x: 20, y: 20})
const chatPosition = ref({x: 20, y: 80})

// 流式输出相关
const isStreaming = ref(false)

// 调整大小相关
const isResizing = ref(false)
const chatSize = ref({width: 450, height: 600})
const minSize = {width: 250, height: 350}
const maxSize = {width: 1200, height: 1000}

// 聊天相关
const messagesContainer = ref<HTMLElement | null>(null)
const floatingButton = ref<HTMLElement | null>(null)
const userInput = ref('')
const isLoading = ref(false)
const chatMessages = ref<Array<{
  type: 'user' | 'ai'
  content: string
  time: Date
}>>([])

// 用户头像
const userAvatar = ref('')

// 快捷操作
const quickActions = [
  {label: '推荐经济型车', text: '请推荐一些经济实惠的车型，预算在200-300元/天'},
  {label: '推荐SUV', text: '我需要一辆SUV，适合5人出行，有什么推荐吗？'},
  {label: '租车流程', text: '请介绍一下租车的完整流程和注意事项'}
]

// 计算样式
const buttonStyle = computed(() => ({
  left: `${buttonPosition.value.x}px`,
  top: `${buttonPosition.value.y}px`
}))

const chatStyle = computed(() => ({
  left: `${chatPosition.value.x}px`,
  top: `${chatPosition.value.y}px`,
  width: `${chatSize.value.width}px`,
  height: `${chatSize.value.height}px`
}))

// 切换聊天窗口
const toggleChat = () => {
  console.log('Toggle chat clicked')
  isChatOpen.value = !isChatOpen.value
  console.log('isChatOpen is now:', isChatOpen.value)
  if (isChatOpen.value) {
    isMinimized.value = false
    nextTick(() => {
      scrollToBottom()
    })
  }
}

// 切换最小化
const toggleMinimize = () => {
  isMinimized.value = !isMinimized.value
  if (!isMinimized.value) {
    nextTick(() => {
      scrollToBottom()
    })
  }
}

// 开始拖动
const startDrag = (event: MouseEvent) => {
  if (event.target && (event.target as HTMLElement).closest('.header-actions')) {
    return // 如果点击的是头部操作按钮，不开始拖动
  }

  isDragging.value = true
  const rect = (event.currentTarget as HTMLElement).getBoundingClientRect()
  dragOffset.x = event.clientX - rect.left
  dragOffset.y = event.clientY - rect.top

  document.addEventListener('mousemove', onDrag)
  document.addEventListener('mouseup', stopDrag)
  event.preventDefault()
}

// 拖动中
const onDrag = (event: MouseEvent) => {
  if (!isDragging.value) return

  const newX = event.clientX - dragOffset.x
  const newY = event.clientY - dragOffset.y

  // 限制在窗口范围内
  const maxX = window.innerWidth - (isChatOpen.value ? (chatSize.value.width + 20) : 120)
  const maxY = window.innerHeight - (isChatOpen.value ? (chatSize.value.height + 20) : 60)

  if (isChatOpen.value) {
    chatPosition.value = {
      x: Math.max(0, Math.min(newX, maxX)),
      y: Math.max(0, Math.min(newY, maxY))
    }
  } else {
    buttonPosition.value = {
      x: Math.max(0, Math.min(newX, maxX)),
      y: Math.max(0, Math.min(newY, maxY))
    }
  }
}

// 停止拖动
const stopDrag = () => {
  isDragging.value = false
  document.removeEventListener('mousemove', onDrag)
  document.removeEventListener('mouseup', stopDrag)
}

// 开始调整大小
const startResize = (event: MouseEvent) => {
  isResizing.value = true
  const startX = event.clientX
  const startY = event.clientY
  const startWidth = chatSize.value.width
  const startHeight = chatSize.value.height

  const onResize = (e: MouseEvent) => {
    const deltaX = e.clientX - startX
    const deltaY = e.clientY - startY

    const newWidth = Math.max(minSize.width, Math.min(maxSize.width, startWidth + deltaX))
    const newHeight = Math.max(minSize.height, Math.min(maxSize.height, startHeight + deltaY))

    chatSize.value = {width: newWidth, height: newHeight}
  }

  const stopResize = () => {
    isResizing.value = false
    document.removeEventListener('mousemove', onResize)
    document.removeEventListener('mouseup', stopResize)
  }

  document.addEventListener('mousemove', onResize)
  document.addEventListener('mouseup', stopResize)
  event.preventDefault()
}

// 发送消息
const sendMessage = async () => {
  if (!userInput.value.trim() || isLoading.value) return

  const userMessage = userInput.value.trim()
  const currentTime = new Date()

  // 添加用户消息
  chatMessages.value.push({
    type: 'user',
    content: userMessage,
    time: currentTime
  })

  // 清空输入框
  userInput.value = ''
  isLoading.value = true

  // 滚动到底部
  await nextTick()
  scrollToBottom()

  try {
    // 获取用户信息
    const userInfo = getUserInfo()
    const userId = userInfo?.id?.toString() || ''

    // 调用AI流式聊天接口
    const params: ChatStreamParams = {
      message: userMessage,
      userId: userId,
      sessionId: currentSessionId.value
    }

    // 添加一个空的AI消息用于流式更新
    const aiMessageIndex = chatMessages.value.length
    chatMessages.value.push({
      type: 'ai',
      content: '',
      time: new Date()
    })

    isStreaming.value = true

    // 调用流式接口
    const response = await chatStream(params)
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const reader = response.body?.getReader()
    if (!reader) {
      throw new Error('无法获取响应流')
    }

    const decoder = new TextDecoder()
    let accumulatedContent = ''

    try {
      while (true) {
        const {done, value} = await reader.read()

        if (done) break

        const chunk = decoder.decode(value, {stream: true})
        accumulatedContent += chunk

        // 更新AI消息内容
        chatMessages.value[aiMessageIndex].content = accumulatedContent

        // 滚动到底部
        await nextTick()
        scrollToBottom()

        // 添加小延迟模拟打字机效果
        await new Promise(resolve => setTimeout(resolve, 10))

      }
    } finally {
      reader.releaseLock()
      isStreaming.value = false
    }

  } catch (error) {
    console.error('AI聊天错误:', error)
    // 如果流式输出失败，显示错误消息
    if (chatMessages.value.length > 0 && chatMessages.value[chatMessages.value.length - 1].type === 'ai' && chatMessages.value[chatMessages.value.length - 1].content === '') {
      chatMessages.value[chatMessages.value.length - 1].content = '抱歉，网络连接出现问题，请稍后重试。'
    } else {
      chatMessages.value.push({
        type: 'ai',
        content: '抱歉，网络连接出现问题，请稍后重试。',
        time: new Date()
      })
    }
  } finally {
    isLoading.value = false
    isStreaming.value = false
    nextTick(() => {
      scrollToBottom()
    })
  }
}

// 发送快捷消息
const sendQuickMessage = (text: string) => {
  userInput.value = text
  sendMessage()
}

// 处理回车键
const handleEnterKey = (event: KeyboardEvent) => {
  if (event.shiftKey) {
    // Shift+Enter 换行
    return
  }
  // 普通回车发送消息
  event.preventDefault()
  sendMessage()
}

// 复制消息功能
const copyMessage = async (content: string) => {
  try {
    await navigator.clipboard.writeText(content)
    ElMessage.success('消息已复制到剪贴板')
  } catch (err) {
    console.error('复制失败:', err)
    // 降级方案：使用传统的复制方法
    const textArea = document.createElement('textarea')
    textArea.value = content
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    ElMessage.success('消息已复制到剪贴板')
  }
}

// 生成会话ID
const generateSessionId = (): string => {
  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

const currentSessionId = ref(generateSessionId())

// 滚动到底部
const scrollToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

// 格式化时间
const formatTime = (time: Date): string => {
  return time.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 初始化 markdown-it 实例
const md = new MarkdownIt({
  html: true,
  linkify: true,
  typographer: true,
  highlight: function (str, lang) {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return hljs.highlight(str, {language: lang}).value;
      } catch (__) {
      }
    }
    return ''; // 使用默认的转义
  }
});

// 使用 markdown-it 渲染 Markdown
const renderMarkdown = (content: string): string => {
  return md.render(content);
};

// 格式化消息内容（保持向后兼容）
const formatMessage = (content: string): string => {
  return renderMarkdown(content)
}

// 监听消息变化，自动滚动
watch(chatMessages, () => {
  nextTick(() => {
    scrollToBottom()
  })
})

// 监听窗口大小变化，调整位置和大小
const handleResize = () => {
  const maxX = window.innerWidth - (isChatOpen.value ? (chatSize.value.width + 20) : 120)
  const maxY = window.innerHeight - (isChatOpen.value ? (chatSize.value.height + 20) : 60)

  if (isChatOpen.value) {
    chatPosition.value = {
      x: Math.min(chatPosition.value.x, maxX),
      y: Math.min(chatPosition.value.y, maxY)
    }

    // 如果窗口太小，调整聊天窗口大小
    if (chatSize.value.width > window.innerWidth - 40) {
      chatSize.value.width = Math.max(minSize.width, window.innerWidth - 40)
    }
    if (chatSize.value.height > window.innerHeight - 40) {
      chatSize.value.height = Math.max(minSize.height, window.innerHeight - 40)
    }
  } else {
    buttonPosition.value = {
      x: Math.min(buttonPosition.value.x, maxX),
      y: Math.min(buttonPosition.value.y, maxY)
    }
  }
}

onMounted(() => {
  // 初始化用户头像
  const userInfo = JSON.parse(sessionStorage.getItem('userinfo') || '{}')
  userAvatar.value = userInfo.avatarUrl || ''

  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.floating-ai-chat {
  position: fixed;
  z-index: 9999;
  pointer-events: none;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.floating-ai-chat > * {
  pointer-events: auto;
}

/* 悬浮按钮 */
.floating-button {
  position: fixed;
  display: flex;
  align-items: center;
  gap: 8px;
  background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
  color: white;
  padding: 12px 16px;
  border-radius: 50px;
  cursor: pointer;
  box-shadow: 0 4px 20px rgba(64, 158, 255, 0.3);
  transition: all 0.3s ease;
  user-select: none;
  z-index: 10000;
}

.floating-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 25px rgba(64, 158, 255, 0.4);
}

.floating-button.chat-open {
  background: linear-gradient(135deg, #F56C6C 0%, #E6A23C 100%);
  box-shadow: 0 4px 20px rgba(245, 108, 108, 0.3);
}

.floating-button.chat-open:hover {
  box-shadow: 0 6px 25px rgba(245, 108, 108, 0.4);
}

.button-text {
  font-size: 14px;
  font-weight: 500;
}

/* 聊天窗口 */
.chat-window {
  position: fixed;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  z-index: 10001;
  transition: all 0.3s ease;
  min-width: 300px;
  min-height: 400px;
}

.chat-window.chat-minimized {
  height: 60px;
}

/* 聊天窗口头部 */
.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
  color: white;
  cursor: move;
  user-select: none;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.ai-icon {
  font-size: 18px;
}

.chat-title {
  font-size: 14px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 4px;
}

.minimize-btn,
.close-btn {
  color: white;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.minimize-btn:hover,
.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* 聊天内容区域 */
.chat-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background: #f8f9fa;
}

.resize-handle {
  position: absolute;
  bottom: 8px;
  right: 8px;
  width: 20px;
  height: 20px;
  background: rgba(64, 158, 255, 0.1);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: nw-resize;
  transition: all 0.2s ease;
  z-index: 10;
  opacity: 0;
}

.chat-window:hover .resize-handle {
  opacity: 1;
}

.resize-handle:hover {
  background: rgba(64, 158, 255, 0.2);
  transform: scale(1.1);
}

.resize-handle svg {
  width: 12px;
  height: 12px;
  color: #409EFF;
}

/* 消息样式 */
.message {
  display: flex;
  margin-bottom: 16px;
  gap: 8px;
}

.user-message {
  flex-direction: row-reverse;
}

.user-message .message-content {
  background: #409EFF;
  color: white;
  border-radius: 12px 12px 4px 12px;
}

.ai-message .message-content {
  background: white;
  color: #303133;
  border-radius: 12px 12px 12px 4px;
  border: 1px solid #E4E7ED;
}

.message-content {
  max-width: 80%;
  padding: 8px 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.message-actions {
  display: flex;
  align-items: center;
  gap: 6px;
}

.copy-btn {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 3px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: rgba(255, 255, 255, 0.7);
  opacity: 0;
}

.message:hover .copy-btn {
  opacity: 1;
}

.copy-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  transform: scale(1.05);
}

.copy-btn svg {
  width: 10px;
  height: 10px;
}

/* AI消息中的复制按钮样式 */
.ai-message .copy-btn {
  background: rgba(64, 158, 255, 0.1);
  color: rgba(64, 158, 255, 0.7);
}

.ai-message .copy-btn:hover {
  background: rgba(64, 158, 255, 0.2);
  color: #409EFF;
}

.message-name {
  font-size: 12px;
  font-weight: 600;
  opacity: 0.8;
}

.message-time {
  font-size: 10px;
  opacity: 0.6;
}

.message-text {
  font-size: 13px;
  line-height: 1.4;
}

.message-text ul {
  margin: 4px 0;
  padding-left: 16px;
}

.message-text li {
  margin-bottom: 2px;
}

.typing-cursor {
  display: inline-block;
  width: 2px;
  height: 1.2em;
  background: #409EFF;
  animation: blink 1s infinite;
  margin-left: 2px;
  vertical-align: middle;
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

/* Markdown样式 - 适配 markdown-it */
.markdown-content {
  font-size: 13px;
  line-height: 1.5;
  word-break: break-word;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  color: #303133;
  font-weight: 600;
  margin: 8px 0 4px 0;
  padding: 0;
  line-height: 1.3;
}

.markdown-content h1 {
  font-size: 16px;
}

.markdown-content h2 {
  font-size: 15px;
}

.markdown-content h3 {
  font-size: 14px;
}

.markdown-content strong {
  color: #303133;
  font-weight: 600;
}

.markdown-content em {
  font-style: italic;
  color: #606266;
}

.markdown-content p {
  margin: 6px 0;
  line-height: 1.4;
  color: #606266;
}

.markdown-content ul,
.markdown-content ol {
  margin: 8px 0;
  padding-left: 16px;
}

.markdown-content li {
  margin: 2px 0;
  line-height: 1.3;
  color: #606266;
}

.markdown-content blockquote {
  border-left: 3px solid #409EFF;
  padding: 6px 12px;
  margin: 8px 0;
  background: rgba(64, 158, 255, 0.05);
  border-radius: 0 4px 4px 0;
  font-style: italic;
  color: #606266;
}

.markdown-content hr {
  border: none;
  border-top: 1px solid #E4E7ED;
  margin: 12px 0;
}

.markdown-content a {
  color: #409EFF;
  text-decoration: underline;
  text-underline-offset: 2px;
}

.markdown-content a:hover {
  color: #66b1ff;
}

.markdown-content code {
  color: #e74c3c;
  font-size: 12px;
  font-weight: 500;
  padding: 2px 4px;
  border-radius: 3px;
  margin: 0 1px;
  background-color: rgba(231, 76, 60, 0.1);
  border: 1px solid rgba(231, 76, 60, 0.2);
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

/* markdown-it 生成的代码块样式 */
.markdown-content pre {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  border: 1px solid #409EFF;
  border-radius: 6px;
  padding: 12px;
  margin: 8px 0;
  overflow-x: auto;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.markdown-content pre code {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  color: #ecf0f1;
  font-size: 12px;
  line-height: 1.4;
  white-space: pre;
  display: block;
  overflow-x: auto;
  font-weight: 400;
}

/* markdown-it 生成的表格样式 */
.markdown-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 8px 0;
  font-size: 12px;
  background: #ffffff;
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid #E4E7ED;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.markdown-content table thead {
  background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
}

.markdown-content table th {
  background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
  font-weight: 600;
  color: #ffffff;
  padding: 8px 12px;
  text-align: left;
  border-bottom: 2px solid rgba(255, 255, 255, 0.2);
  font-size: 12px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.markdown-content table td {
  color: #2c3e50;
  padding: 6px 12px;
  border-bottom: 1px solid #e1e8ed;
  line-height: 1.3;
  background: #ffffff;
}

.markdown-content table tbody tr:nth-child(even) td {
  background: #f8f9fa;
}

.markdown-content table tbody tr:hover td {
  background: #e3f2fd;
  transition: background-color 0.2s ease;
}

/* 用户消息中的markdown样式 */
.user-message .markdown-content h1,
.user-message .markdown-content h2,
.user-message .markdown-content h3,
.user-message .markdown-content h4,
.user-message .markdown-content h5,
.user-message .markdown-content h6 {
  color: rgba(255, 255, 255, 0.95);
}

.user-message .markdown-content strong {
  color: rgba(255, 255, 255, 0.95);
}

.user-message .markdown-content em {
  color: rgba(255, 255, 255, 0.9);
}

.user-message .markdown-content p {
  color: rgba(255, 255, 255, 0.95);
}

.user-message .markdown-content li {
  color: rgba(255, 255, 255, 0.95);
}

.user-message .markdown-content blockquote {
  border-left-color: rgba(255, 255, 255, 0.6);
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
}

.user-message .markdown-content hr {
  border-top-color: rgba(255, 255, 255, 0.3);
}

.user-message .markdown-content a {
  color: rgba(255, 255, 255, 0.9);
  text-decoration-color: rgba(255, 255, 255, 0.6);
}

.user-message .markdown-content a:hover {
  color: #ffffff;
  text-decoration-color: rgba(255, 255, 255, 0.8);
}

.user-message .markdown-content code {
  color: #ff6b6b;
  background-color: rgba(255, 107, 107, 0.2);
  border: 1px solid rgba(255, 107, 107, 0.3);
}

.user-message .markdown-content pre {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.user-message .markdown-content pre code {
  color: rgba(255, 255, 255, 0.9);
}

.user-message .markdown-content table {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.user-message .markdown-content table th {
  background: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.95);
}

.user-message .markdown-content table td {
  color: rgba(255, 255, 255, 0.9);
  background: rgba(255, 255, 255, 0.05);
}

.user-message .markdown-content table tbody tr:nth-child(even) td {
  background: rgba(255, 255, 255, 0.1);
}

.user-message .markdown-content table tbody tr:hover td {
  background: rgba(255, 255, 255, 0.2);
}

/* 打字指示器 */
.typing-indicator {
  display: flex;
  gap: 3px;
  align-items: center;
}

.typing-indicator span {
  width: 6px;
  height: 6px;
  background: #409EFF;
  border-radius: 50%;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 输入区域 */
.input-area {
  padding: 12px;
  background: white;
  border-top: 1px solid #E4E7ED;
}

.input-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.input-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.quick-actions {
  display: flex;
  gap: 4px;
}

.quick-actions .el-button {
  font-size: 11px;
  padding: 4px 8px;
  height: 24px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-window {
    min-width: 320px !important;
    max-width: 380px !important;
    min-height: 400px !important;
    max-height: 520px !important;
  }

  .floating-button {
    padding: 10px 14px;
  }

  .button-text {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .chat-window {
    min-width: 280px !important;
    max-width: 320px !important;
    min-height: 350px !important;
    max-height: 450px !important;
  }

  .floating-button {
    padding: 8px 12px;
  }

  .button-text {
    display: none;
  }
}
</style>
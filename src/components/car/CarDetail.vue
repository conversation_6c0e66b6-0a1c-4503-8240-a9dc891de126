<template>
  <div class="car-detail-page">
    <div class="container">
      <!-- 返回按钮 -->
      <div class="back-section">
        <el-button 
          type="text" 
          @click="$router.go(-1)"
          class="back-btn"
        >
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-section">
        <el-skeleton :rows="10" animated />
      </div>

      <!-- 汽车详情 -->
      <div v-else-if="car" class="car-detail">
        <!-- 汽车图片展示 -->
        <div class="car-images">
          <div class="main-image" @click="showImagePreview">
            <img 
              v-if="carImages[activeImageIndex]?.url" 
              :src="carImages[activeImageIndex].url" 
              :alt="car.name"
              class="main-car-image"
            />
            <div v-else class="image-placeholder">
              <el-icon class="car-icon" :size="80">
                <Van />
              </el-icon>
            </div>
            <div v-if="carImages[activeImageIndex]?.url" class="zoom-hint">
              <el-icon><ZoomIn /></el-icon>
              <span>点击放大</span>
            </div>
          </div>
          <div class="image-gallery">
            <div 
              v-for="(image, index) in carImages" 
              :key="index"
              class="gallery-item"
              :class="{ active: activeImageIndex === index }"
              @click="activeImageIndex = index"
            >
              <img 
                v-if="image.url" 
                :src="image.url" 
                :alt="`${car.name} - 图${index + 1}`"
                class="gallery-image"
              />
              <div v-else class="gallery-placeholder">
                <el-icon class="car-icon-small" :size="24">
                  <Van />
                </el-icon>
              </div>
            </div>
          </div>
        </div>

        <!-- 汽车信息 -->
        <div class="car-info">
          <div class="car-header">
            <h1 class="car-title">{{ car.name }}</h1>
            <div class="car-price">
              <span class="price-amount">¥{{ car.dailyRate }}</span>
              <span class="price-unit">/天</span>
            </div>
          </div>

          <!-- 汽车参数 -->
          <div class="car-specs">
            <h3 class="specs-title">车辆参数</h3>
            <div class="specs-grid">
              <div class="spec-item">
                <span class="spec-label">品牌</span>
                <span class="spec-value">{{ car.brand || '未知' }}</span>
              </div>
              <div class="spec-item">
                <span class="spec-label">发动机</span>
                <span class="spec-value">{{ car.engine }}</span>
              </div>
              <div class="spec-item">
                <span class="spec-label">座位数</span>
                <span class="spec-value">{{ car.seats }}座</span>
              </div>
              <div class="spec-item">
                <span class="spec-label">变速箱</span>
                <span class="spec-value">{{ car.transmission }}</span>
              </div>
              <div class="spec-item">
                <span class="spec-label">年份</span>
                <span class="spec-value">{{ car.year || '未知' }}</span>
              </div>
              <div class="spec-item">
                <span class="spec-label">里程</span>
                <span class="spec-value">{{ car.mileage ? `${car.mileage}km` : '未知' }}</span>
              </div>
              <div class="spec-item">
                <span class="spec-label">燃料类型</span>
                <span class="spec-value">{{ car.fuelType || '未知' }}</span>
              </div>
              <div class="spec-item">
                <span class="spec-label">状态</span>
                <el-tag :type="car.available ? 'success' : 'danger'">
                  {{ car.available ? '可租赁' : '已租出' }}
                </el-tag>
              </div>
            </div>
          </div>

          <!-- 租赁表单 -->
          <div class="rental-section">
            <h3 class="rental-title">租赁信息</h3>
            <el-form 
              ref="rentalFormRef"
              :model="rentalForm"
              :rules="rentalRules"
              label-width="100px"
              class="rental-form"
            >
              <el-form-item label="租赁日期" prop="dateRange">
                <el-date-picker
                  v-model="rentalForm.dateRange"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :disabled-date="disabledDate"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  style="width: 100%"
                />
              </el-form-item>
              
              <el-form-item label="租赁天数">
                <span class="rental-days">{{ rentalDays }}天</span>
              </el-form-item>
              
              <el-form-item label="总费用">
                <span class="total-price">¥{{ totalPrice }}</span>
              </el-form-item>
              
              <el-form-item>
                <el-button 
                  type="primary" 
                  size="large"
                  :loading="renting"
                  :disabled="!car.available"
                  @click="handleRent"
                  class="rent-btn"
                >
                  {{ car.available ? '立即租赁' : '暂不可租' }}
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </div>

      <!-- 错误状态 -->
      <div v-else class="error-section">
        <el-empty description="汽车信息不存在">
          <el-button type="primary" @click="$router.push('/')">
            返回首页
          </el-button>
        </el-empty>
      </div>
    </div>

    <!-- 图片预览对话框 -->
    <el-dialog 
      v-model="showPreview" 
      :show-close="false" 
      :width="'90%'" 
      :style="{ maxWidth: '1000px' }"
      class="image-preview-dialog"
    >
      <div class="preview-container">
        <img 
          v-if="carImages[activeImageIndex]?.url" 
          :src="carImages[activeImageIndex].url" 
          :alt="car?.name"
          class="preview-image"
        />
        <div class="preview-controls">
          <el-button 
            v-for="(image, index) in carImages"
            :key="index"
            :type="activeImageIndex === index ? 'primary' : ''"
            size="small"
            @click="activeImageIndex = index"
            class="preview-thumb"
          >
            {{ index + 1 }}
          </el-button>
        </div>
        <el-button 
          @click="showPreview = false" 
          class="close-preview"
          circle
          size="large"
        >
          <el-icon><Close /></el-icon>
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, Van, ZoomIn, Close } from '@element-plus/icons-vue'
import { queryCarPage } from '@/services/carApi.ts'
import { transformCarData } from '@/config/api.ts'
import { addOrders } from '@/services/ordersApi.ts'
import { getUserInfo } from '@/utils/auth.ts'

// 定义Car类型（从数据库返回的数据结构）
interface Car {
  id: number
  name: string
  engine: string
  seats: number
  transmission: string
  dailyRate: number
  brand: string
  year: number
  mileage: number
  fuelType: string
  available: boolean
  image: string
  status: string
}

// 路由参数
const route = useRoute()
const router = useRouter()
const carId = computed(() => Number(route.params.id))

// 响应式数据
const car = ref<Car | null>(null)
const loading = ref(true)
const renting = ref(false)
const activeImageIndex = ref(0)
const showPreview = ref(false)

// 租赁表单
const rentalFormRef = ref()
const rentalForm = ref({
  dateRange: [] as [Date, Date] | []
})

// 表单验证规则
const rentalRules = {
  dateRange: [
    { required: true, message: '请选择租赁日期', trigger: 'change' }
  ]
}

// 汽车图片
const carImages = ref([
  { id: 1, url: '' },
  { id: 2, url: '' },
  { id: 3, url: '' },
  { id: 4, url: '' },
  { id: 5, url: '' }
])

// 根据数据库中查询到的图片加载图片
const loadCarImages = (carImage: string) => {
  // 所有5个图片都使用数据库中查询到的那一张图片
  carImages.value = [
    { id: 1, url: carImage },
    { id: 2, url: carImage },
    { id: 3, url: carImage },
    { id: 4, url: carImage },
    { id: 5, url: carImage }
  ]
}

// 计算属性
const rentalDays = computed(() => {
  if (!rentalForm.value.dateRange || rentalForm.value.dateRange.length !== 2) {
    return 0
  }
  const [start, end] = rentalForm.value.dateRange
  
  // 需要将时间转换为Date对象
  const startDate = new Date(start)
  const endDate = new Date(end)
  
  console.log('开始时间:', startDate)
  console.log('结束时间:', endDate)
  
  // 计算时间差（毫秒）
  const diffTime = Math.abs(endDate.getTime() - startDate.getTime())
  
  // 转换为天数，按24小时计算
  const days = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  
  // 如果不足24小时但跨天，按1天计算
  // 如果超过24小时，按实际天数计算
  return days === 0 ? 1 : days
})

const totalPrice = computed(() => {
  if (!car.value) return 0
  return rentalDays.value * car.value.dailyRate
})

// 禁用日期时间（当前时间之前的日期时间）
const disabledDate = (time: Date) => {
  return time.getTime() < Date.now()
}

// 显示图片预览
const showImagePreview = () => {
  if (carImages.value[activeImageIndex.value]?.url) {
    showPreview.value = true
  }
}

// 获取汽车详情
const fetchCarDetail = async () => {
  loading.value = true
  try {
    // 从数据库获取具体的车辆信息
    const response = await queryCarPage({
      pageNum: 1,
      pageSize: 100,
      params: {
        id: carId.value
      }
    })
    
    if (response.code === 0 && response.data?.list && response.data.list.length > 0) {
      const dbCar = response.data.list[0]
      car.value = transformCarData(dbCar)
      loadCarImages(car.value.image)
    } else {
      ElMessage.error(response.description || '汽车信息不存在')
    }
  } catch (error) {
    ElMessage.error('获取汽车信息失败')
    console.error('获取汽车详情错误:', error)
  } finally {
    loading.value = false
  }
}

// 获取当前用户ID
const getCurrentUserId = (): number => {
  const userInfo = getUserInfo()
  return userInfo?.id || 0
}

// 生成订单号
const generateOrderNumber = (): string => {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const hours = String(now.getHours()).padStart(2, '0')
  const minutes = String(now.getMinutes()).padStart(2, '0')
  const seconds = String(now.getSeconds()).padStart(2, '0')
  const milliseconds = String(now.getMilliseconds()).padStart(3, '0')
  
  // 格式：ORD + 年月日时分秒毫秒 + 随机数
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
  return `ORD${year}${month}${day}${hours}${minutes}${seconds}${milliseconds}${random}`
}

// 租赁处理
const handleRent = async () => {
  if (!car.value) return
  
  try {
    await rentalFormRef.value.validate()
    
    const userId = getCurrentUserId()
    if (!userId) {
      ElMessage.error('用户未登录')
      return
    }
    
    renting.value = true
    
    // 调用真实的租赁API
    const [startDate, endDate] = rentalForm.value.dateRange
    if (!startDate || !endDate) {
      ElMessage.error('请选择租赁日期')
      return
    }
    
    const orderData = {
      carId: car.value.id,
      carName: car.value.name,
      userId: userId,
      orderNumber: generateOrderNumber(),
      startDate: startDate,
      endDate: endDate,
      totalDays: rentalDays.value,
      dailyRate: car.value.dailyRate,
      totalAmount: totalPrice.value,
      status: 'pending',
      contactPhone: getUserInfo()?.phone || '',
      remark: `租赁${car.value.name}，${rentalDays.value}天`
    }
    
    const response = await addOrders(orderData)
    
    if (response.code === 0) {
      await ElMessageBox.alert(
        `汽车：${car.value.name}\n租赁天数：${rentalDays.value}天\n总费用：¥${totalPrice.value}\n\n订单已创建，请及时支付`,
        '租赁申请提交成功',
        {
          confirmButtonText: '确定',
          type: 'success'
        }
      )
      
      // 跳转到订单页面
      await router.push('/orders')
    } else {
      ElMessage.error(response.message || '租赁申请失败')
    }
    
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('租赁申请失败，请重试')
      console.error('租赁错误:', error)
    }
  } finally {
    renting.value = false
  }
}

onMounted(() => {
  fetchCarDetail()
})
</script>

<style scoped>
.car-detail-page {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding: 40px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.back-section {
  margin-bottom: 24px;
}

.back-btn {
  font-size: 16px;
  color: #409EFF;
}

.back-btn:hover {
  color: #66b1ff;
}

.loading-section {
  background: white;
  border-radius: 12px;
  padding: 32px;
}

.car-detail {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 48px;
  background: white;
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.car-images {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.main-image {
  aspect-ratio: 16/9;
  border-radius: 12px;
  overflow: hidden;
  min-height: 400px;
  cursor: zoom-in;
  position: relative;
}

.main-car-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  transition: transform 0.3s ease;
}

.main-image:hover .main-car-image {
  transform: scale(1.02);
}

.zoom-hint {
  position: absolute;
  bottom: 12px;
  right: 12px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.main-image:hover .zoom-hint {
  opacity: 1;
}

.image-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.car-icon {
  color: white;
  opacity: 0.9;
}

.image-gallery {
  display: flex;
  gap: 12px;
}

.gallery-item {
  width: 120px;
  height: 90px;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.gallery-item.active {
  border-color: #409EFF;
}

.gallery-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.gallery-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #E6A23C 0%, #F56C6C 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.car-icon-small {
  color: white;
  opacity: 0.8;
}

.car-info {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.car-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 16px;
}

.car-title {
  font-family: 'Poppins', sans-serif;
  font-size: 32px;
  font-weight: 700;
  color: #303133;
  margin: 0;
  flex: 1;
}

.car-price {
  text-align: right;
}

.price-amount {
  font-size: 32px;
  font-weight: 700;
  color: #FF6700;
}

.price-unit {
  font-size: 16px;
  color: #909399;
}

.car-specs {
  border-top: 1px solid #EBEEF5;
  padding-top: 24px;
}

.specs-title {
  font-family: 'Poppins', sans-serif;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 16px 0;
}

.specs-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.spec-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f5f7fa;
  border-radius: 8px;
}

.spec-label {
  font-size: 14px;
  color: #606266;
}

.spec-value {
  font-weight: 500;
  color: #303133;
}

.rental-section {
  border-top: 1px solid #EBEEF5;
  padding-top: 24px;
}

.rental-title {
  font-family: 'Poppins', sans-serif;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 16px 0;
}

.rental-form {
  max-width: 400px;
}

.rental-days {
  font-size: 18px;
  font-weight: 600;
  color: #409EFF;
}

.total-price {
  font-size: 24px;
  font-weight: 700;
  color: #FF6700;
}

.rent-btn {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 500;
}

.error-section {
  background: white;
  border-radius: 12px;
  padding: 60px 32px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .car-detail {
    grid-template-columns: 1fr;
    gap: 32px;
    padding: 24px;
  }
  
  .car-title {
    font-size: 24px;
  }
  
  .price-amount {
    font-size: 24px;
  }
  
  .specs-grid {
    grid-template-columns: 1fr;
  }
  
  .car-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .car-price {
    text-align: left;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 16px;
  }
  
  .car-detail {
    padding: 20px;
  }
  
  .image-gallery {
    gap: 8px;
  }
  
  .gallery-item {
    width: 60px;
    height: 45px;
  }
}

/* 图片预览样式 */
.image-preview-dialog :deep(.el-dialog) {
  margin: 0;
  border-radius: 12px;
}

.image-preview-dialog :deep(.el-dialog__body) {
  padding: 0;
}

.preview-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #000;
  border-radius: 12px;
}

.preview-image {
  max-width: 100%;
  max-height: 70vh;
  object-fit: contain;
  border-radius: 12px 12px 0 0;
}

.preview-controls {
  padding: 16px;
  background: white;
  border-radius: 0 0 12px 12px;
  width: 100%;
  display: flex;
  justify-content: center;
  gap: 8px;
  flex-wrap: wrap;
}

.preview-thumb {
  min-width: 40px;
}

.close-preview {
  position: absolute;
  top: 16px;
  right: 16px;
  background: rgba(0, 0, 0, 0.7);
  border: none;
  color: white;
}

.close-preview:hover {
  background: rgba(0, 0, 0, 0.9);
}
</style> 
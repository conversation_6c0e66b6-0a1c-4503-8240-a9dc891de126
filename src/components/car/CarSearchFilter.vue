<template>
  <div class="search-filter">
    <el-card class="filter-card" shadow="hover">
      <template #header>
        <div class="filter-header">
          <el-icon>
            <Search/>
          </el-icon>
          <span>筛选条件</span>
          <el-button
              type="text"
              size="small"
              @click="resetFilters"
              class="reset-btn"
          >
            重置
          </el-button>
        </div>
      </template>

      <el-form :model="filters" label-width="80px" class="filter-form">
        <el-row :gutter="20">
          <!-- 车辆名称 -->
          <el-col :span="6">
            <el-form-item label="车辆名称">
              <el-input
                  v-model="filters.name"
                  placeholder="请输入车辆名称"
                  clearable
                  @clear="handleSearch"
                  @keyup.enter="handleSearch"
              >
                <template #prefix>
                  <el-icon>
                    <Search/>
                  </el-icon>
                </template>
              </el-input>
            </el-form-item>
          </el-col>

          <!-- 品牌 -->
          <el-col :span="6">
            <el-form-item label="品牌">
              <el-select
                  v-model="filters.brand"
                  placeholder="请选择品牌"
                  clearable
                  @change="handleSearch"
                  @clear="handleSearch"
              >
                <el-option
                    v-for="brand in brandOptions"
                    :key="brand.value"
                    :label="brand.label"
                    :value="brand.value"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 座位数 -->
          <el-col :span="6">
            <el-form-item label="座位数">
              <el-select
                  v-model="filters.seats"
                  placeholder="请选择座位数"
                  clearable
                  @change="handleSearch"
                  @clear="handleSearch"
              >
                <el-option
                    v-for="seat in seatOptions"
                    :key="seat.value"
                    :label="seat.label"
                    :value="seat.value"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 变速箱 -->
          <el-col :span="6">
            <el-form-item label="变速箱">
              <el-select
                  v-model="filters.transmission"
                  placeholder="请选择变速箱"
                  clearable
                  @change="handleSearch"
                  @clear="handleSearch"
              >
                <el-option
                    v-for="trans in transmissionOptions"
                    :key="trans.value"
                    :label="trans.label"
                    :value="trans.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <!-- 燃料类型 -->
          <el-col :span="6">
            <el-form-item label="燃料类型">
              <el-select
                  v-model="filters.fuelType"
                  placeholder="请选择燃料类型"
                  clearable
                  @change="handleSearch"
                  @clear="handleSearch"
                  class="fuel-type-select"
              >
                <el-option
                    v-for="fuel in fuelTypeOptions"
                    :key="fuel.value"
                    :label="fuel.label"
                    :value="fuel.value"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 价格范围 -->
          <el-col :span="10">
            <el-form-item label="价格范围">
              <div class="price-range">
                <el-input-number
                    v-model="filters.dailyRateMin"
                    :min="0"
                    :max="filters.dailyRateMax || 10000"
                    placeholder="最低价"
                    @change="handleSearch"
                    class="price-input"
                />
                <span class="range-separator">至</span>
                <el-input-number
                    v-model="filters.dailyRateMax"
                    :min="filters.dailyRateMin || 0"
                    :max="10000"
                    placeholder="最高价"
                    @change="handleSearch"
                    class="price-input"
                />
              </div>
            </el-form-item>
          </el-col>

          <!-- 年份范围 -->
          <el-col :span="8">
            <el-form-item label="生产年份">
              <el-select
                  v-model="filters.year"
                  placeholder="请选择年份"
                  clearable
                  @change="handleSearch"
                  @clear="handleSearch"
              >
                <el-option
                    v-for="year in yearOptions"
                    :key="year.value"
                    :label="year.label"
                    :value="year.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import {ref, reactive} from 'vue'

// 定义筛选条件接口
interface FilterParams {
  name?: string
  brand?: string
  seats?: number
  transmission?: string
  fuelType?: string
  dailyRateMin?: number
  dailyRateMax?: number
  year?: number
}

// Props
interface Props {
  loading?: boolean
}
withDefaults(defineProps<Props>(), {
  loading: false
});
// Emits
const emit = defineEmits<{
  search: [filters: FilterParams]
  reset: []
}>()

// 筛选条件
const filters = reactive<FilterParams>({
  name: '',
  brand: '',
  seats: undefined,
  transmission: '',
  fuelType: '',
  dailyRateMin: undefined,
  dailyRateMax: undefined,
  year: undefined
})

// 选项数据
const brandOptions = ref([
  {label: '特斯拉', value: '特斯拉'},
  {label: '奔驰', value: '奔驰'},
  {label: '宝马', value: '宝马'},
  {label: '奥迪', value: '奥迪'},
  {label: '丰田', value: '丰田'},
  {label: '本田', value: '本田'},
  {label: '大众', value: '大众'},
  {label: '日产', value: '日产'},
  {label: '凯迪拉克', value: '凯迪拉克'},
  {label: '雷克萨斯', value: '雷克萨斯'},
  {label: '沃尔沃', value: '沃尔沃'},
  {label: '保时捷', value: '保时捷'},
  {label: '马自达', value: '马自达'},
  {label: '别克', value: '别克'},
  {label: '现代', value: '现代'},
  {label: '起亚', value: '起亚'},
  {label: '英菲尼迪', value: '英菲尼迪'},
  {label: '捷豹', value: '捷豹'},
  {label: '路虎', value: '路虎'},
  {label: '林肯', value: '林肯'},
  {label: '迈巴赫', value: '迈巴赫'},
  {label: '宾利', value: '宾利'},
  {label: '劳斯莱斯', value: '劳斯莱斯'},
  {label: '法拉利', value: '法拉利'},
  {label: '兰博基尼', value: '兰博基尼'},
  {label: '迈凯伦', value: '迈凯伦'},
  {label: '阿斯顿马丁', value: '阿斯顿马丁'},
  {label: '玛莎拉蒂', value: '玛莎拉蒂'}
])

const seatOptions = ref([
  {label: '2座', value: 2},
  {label: '4座', value: 4},
  {label: '5座', value: 5},
  {label: '7座', value: 7},
  {label: '8座及以上', value: 8}
])

const transmissionOptions = ref([
  {label: '手动挡', value: '手动'},
  {label: '自动挡', value: '自动'},
  {label: 'CVT', value: 'CVT'},
  {label: '双离合', value: '双离合'}
])

const fuelTypeOptions = ref([
  {label: '汽油', value: '汽油'},
  {label: '柴油', value: '柴油'},
  {label: '电动', value: '电动'},
  {label: '混合动力', value: '混合动力'},
  {label: '插电式混合动力', value: '插电式混合动力'}
])

const yearOptions = ref([
  {label: '2024年', value: 2024},
  {label: '2023年', value: 2023},
  {label: '2022年', value: 2022},
  {label: '2021年', value: 2021},
  {label: '2020年', value: 2020},
  {label: '2019年', value: 2019},
  {label: '2018年', value: 2018},
  {label: '2017年及以前', value: 2017}
])

// 处理搜索
const handleSearch = () => {
  // 过滤掉空值
  const searchParams: FilterParams = {}

  if (filters.name?.trim()) searchParams.name = filters.name.trim()
  if (filters.brand && filters.brand.trim()) searchParams.brand = filters.brand
  if (filters.seats !== undefined && filters.seats !== null) searchParams.seats = filters.seats
  if (filters.transmission && filters.transmission.trim()) searchParams.transmission = filters.transmission
  if (filters.fuelType && filters.fuelType.trim()) searchParams.fuelType = filters.fuelType
  if (filters.dailyRateMin !== undefined && filters.dailyRateMin !== null && filters.dailyRateMin >= 0) {
    searchParams.dailyRateMin = filters.dailyRateMin
  }
  if (filters.dailyRateMax !== undefined && filters.dailyRateMax !== null && filters.dailyRateMax >= 0) {
    searchParams.dailyRateMax = filters.dailyRateMax
  }
  if (filters.year !== undefined && filters.year !== null) searchParams.year = filters.year

  emit('search', searchParams)
}

// 重置筛选条件
const resetFilters = () => {
  filters.name = ''
  filters.brand = ''
  filters.seats = undefined
  filters.transmission = ''
  filters.fuelType = ''
  filters.dailyRateMin = undefined
  filters.dailyRateMax = undefined
  filters.year = undefined

  emit('reset')
}

// 暴露方法给父组件
defineExpose({
  resetFilters,
  handleSearch
})
</script>

<style scoped>
.search-filter {
  margin-bottom: 24px;
}

.filter-card {
  border-radius: 12px;
  border: 1px solid #e4e7ed;
}

.filter-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: 600;
  color: #303133;
}

.filter-header .el-icon {
  margin-right: 8px;
  color: #409eff;
}

.reset-btn {
  color: #909399;
  padding: 0;
}

.reset-btn:hover {
  color: #409eff;
}

.filter-form {
  margin-top: 16px;
}

.filter-form :deep(.el-form-item) {
  margin-bottom: 16px;
}

.filter-form :deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

.price-range {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 400px;
}

.price-input {
  flex: 1;
  min-width: 140px;
  max-width: 180px;
}

.range-separator {
  color: #909399;
  font-size: 14px;
  white-space: nowrap;
}

.fuel-type-select {
  width: 100%;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .price-input {
    min-width: 120px;
    max-width: 150px;
  }
}

@media (max-width: 768px) {
  .filter-form :deep(.el-col) {
    margin-bottom: 16px;
  }

  .price-range {
    flex-direction: column;
    gap: 8px;
    min-width: auto;
  }

  .price-input {
    max-width: 100%;
    min-width: 180px;
  }

  .range-separator {
    display: none;
  }
}

@media (max-width: 480px) {
  .filter-form :deep(.el-form-item__label) {
    font-size: 14px;
  }

  .price-input {
    min-width: 160px;
  }
}
</style> 
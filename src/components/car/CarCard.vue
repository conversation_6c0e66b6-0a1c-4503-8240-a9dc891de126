<template>
  <el-card 
    class="car-card" 
    :body-style="{ padding: '12px' }"
    shadow="hover"
    @click="handleCardClick"
  >
    <!-- 汽车图片 -->
    <div class="car-image" :style="{ backgroundImage: car.image ? `url(${car.image})` : 'none' }">
      <el-icon v-if="!car.image" class="car-icon" :size="48">
        <Van />
      </el-icon>
    </div>
    
    <!-- 汽车信息 -->
    <div class="car-info">
      <h3 class="car-title">{{ car.name }}</h3>
      
      <!-- 关键参数 -->
      <div class="car-specs">
        <el-tag size="small" type="info">{{ car.engine }}</el-tag>
        <el-tag size="small" type="info">{{ car.seats }}座</el-tag>
        <el-tag size="small" type="info">{{ car.transmission }}</el-tag>
      </div>
      
      <!-- 日租金 -->
      <div class="car-price">
        <span class="price-label">日租金</span>
        <span class="price-amount">¥{{ car.dailyRate }}</span>
      </div>
      
      <!-- 租赁按钮 -->
      <el-button 
        type="primary" 
        class="rent-btn"
        @click="handleRent"
        :loading="loading"
      >
        立即租赁
      </el-button>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { Van } from '@element-plus/icons-vue'
// 定义Car类型（从数据库返回的数据结构）
interface Car {
  id: number
  name: string
  engine: string
  seats: number
  transmission: string
  dailyRate: number
  brand: string
  year: number
  mileage: number
  fuelType: string
  available: boolean
  image: string
  status: string
}

// 组件属性
interface Props {
  car: Car
}

const props = defineProps<Props>()
const router = useRouter()

// 响应式数据
const loading = ref(false)

// 卡片点击处理
const handleCardClick = (event: Event) => {
  // 如果点击的是按钮，不跳转
  if ((event.target as HTMLElement).closest('.rent-btn')) {
    return
  }
  // 跳转到详情页
  router.push(`/car/${props.car.id}`)
}

// 租赁处理函数
const handleRent = async () => {
  // 直接跳转到详情页进行租赁
  await router.push(`/car/${props.car.id}`)
}
</script>

<style scoped>
.car-card {
  transition: all 0.3s ease;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
}

.car-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(64, 158, 255, 0.15);
}

.car-image {
  height: 160px;
  background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: -12px -12px 12px -12px;
  position: relative;
}

.car-icon {
  color: white;
  opacity: 0.9;
}

.car-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.car-title {
  font-family: 'Poppins', sans-serif;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin: 0;
  line-height: 1.4;
}

.car-specs {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.car-price {
  display: flex;
  align-items: baseline;
  gap: 8px;
}

.price-label {
  font-size: 14px;
  color: #909399;
}

.price-amount {
  font-size: 20px;
  font-weight: 600;
  color: #FF6700;
}

.rent-btn {
  width: 100%;
  height: 40px;
  border-radius: 8px;
  font-weight: 500;
}
</style> 
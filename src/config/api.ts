// 车辆状态映射
export const CAR_STATUS = {
    AVAILABLE: 1,
    UNAVAILABLE: 0
} as const

// 订单状态文本映射
export const ORDER_STATUS_TEXT = {
    pending: '待支付',
    paid: '已支付',
    completed: '已完成',
    cancelled: '已取消'
} as const

// 车辆数据转换工具函数
export const transformCarData = (dbCar: any) => ({
    id: dbCar.id,
    name: dbCar.name,
    engine: dbCar.engine,
    seats: dbCar.seats,
    transmission: dbCar.transmission,
    dailyRate: dbCar.dailyRate,
    brand: dbCar.brand,
    year: dbCar.year,
    mileage: dbCar.mileage,
    fuelType: dbCar.fuelType,
    available: dbCar.available === CAR_STATUS.AVAILABLE,
    image: dbCar.image || '',
    status: dbCar.available === CAR_STATUS.AVAILABLE ? '可租赁' : '已租出'
})

// 订单数据转换工具函数
export const transformOrderData = (dbOrder: any) => ({
    id: dbOrder.id,
    carId: dbOrder.carId,
    carName: dbOrder.carName,
    userId: dbOrder.userId,
    orderNumber: dbOrder.orderNumber,
    startDate: dbOrder.startDate,
    endDate: dbOrder.endDate,
    totalDays: dbOrder.totalDays,
    dailyRate: dbOrder.dailyRate,
    totalAmount: dbOrder.totalAmount,
    status: dbOrder.status,
    statusText: ORDER_STATUS_TEXT[dbOrder.status as keyof typeof ORDER_STATUS_TEXT] || dbOrder.status,
    paymentMethod: dbOrder.paymentMethod,
    paymentTime: dbOrder.paymentTime ? dbOrder.paymentTime : null,
    pickupLocation: dbOrder.pickupLocation,
    returnLocation: dbOrder.returnLocation,
    contactPhone: dbOrder.contactPhone,
    remark: dbOrder.remark,
    createdAt: dbOrder.createdAt,
    updatedAt: dbOrder.updatedAt,
    isDelete: dbOrder.isDelete
})
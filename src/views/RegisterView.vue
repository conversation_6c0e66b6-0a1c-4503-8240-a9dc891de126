<template>
  <div class="register-page">
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="geometric-shape shape-1"></div>
      <div class="geometric-shape shape-2"></div>
      <div class="geometric-shape shape-3"></div>
    </div>

    <!-- 注册卡片 -->
    <div class="register-card">
      <div class="card-header">
        <h1 class="logo">用户注册</h1>
        <p class="subtitle">加入我们，开启智能出行之旅</p>
      </div>

      <el-form
          ref="registerFormRef"
          :model="registerForm"
          :rules="registerFormRules"
          class="register-form"
      >
        <el-form-item prop="userAccount">
          <el-input
              v-model="registerForm.userAccount"
              placeholder="请输入用户名"
              size="large"
              :prefix-icon="User"
          />
        </el-form-item>

        <el-form-item prop="userPassword">
          <el-input
              v-model="registerForm.userPassword"
              type="password"
              placeholder="请输入密码"
              size="large"
              :prefix-icon="Lock"
          />
        </el-form-item>

        <el-form-item prop="userCheckPassword">
          <el-input
              v-model="registerForm.userCheckPassword"
              type="password"
              placeholder="请确认密码"
              size="large"
              :prefix-icon="Lock"
          />
        </el-form-item>

        <el-form-item prop="agreement">
          <div class="agreement-container">
            <el-checkbox v-model="registerForm.agreement"></el-checkbox>
            <span class="agreement-text" style="font-size: 13px">
              我已阅读并同意
              <el-link type="primary" @click="showAgreement" style="font-size: 13px">《用户协议》</el-link>
              和
              <el-link type="primary" @click="showPrivacy" style="font-size: 13px">《隐私政策》</el-link>
            </span>
          </div>
        </el-form-item>

        <el-form-item>
          <el-button
              type="primary"
              size="large"
              :loading="loading"
              :disabled="!isFormValid"
              @click="handleRegister"
              class="register-button"
          >
            立即注册
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 底部链接 -->
      <div class="card-footer">
        <p class="login-link">
          已有账号？
          <el-link type="primary" @click="goToLogin">立即登录</el-link>
        </p>
      </div>
    </div>

    <!-- 用户协议对话框 -->
    <el-dialog
        v-model="showAgreementDialog"
        title="用户协议"
        width="600px"
        class="agreement-dialog"
    >
      <div class="agreement-content">
        <h3>汽车租赁系统用户协议</h3>
        <p>欢迎使用汽车租赁系统！在使用我们的服务之前，请您仔细阅读以下协议条款：</p>

        <h4>1. 服务说明</h4>
        <p>本系统提供汽车租赁服务，用户可以通过平台浏览、选择和租赁汽车。</p>

        <h4>2. 用户责任</h4>
        <p>用户应当：</p>
        <ul>
          <li>提供真实、准确的个人信息</li>
          <li>妥善保管账户信息</li>
          <li>遵守相关法律法规</li>
          <li>按时支付租赁费用</li>
        </ul>

        <h4>3. 隐私保护</h4>
        <p>我们承诺保护用户隐私，不会泄露用户个人信息。</p>

        <h4>4. 免责声明</h4>
        <p>因不可抗力因素导致的服务中断，我们不承担责任。</p>
      </div>
    </el-dialog>

    <!-- 隐私政策对话框 -->
    <el-dialog
        v-model="showPrivacyDialog"
        title="隐私政策"
        width="600px"
        class="privacy-dialog"
    >
      <div class="privacy-content">
        <h3>隐私政策</h3>
        <p>我们重视您的隐私保护，本政策说明了我们如何收集、使用和保护您的个人信息：</p>

        <h4>1. 信息收集</h4>
        <p>我们收集的信息包括：</p>
        <ul>
          <li>注册信息（用户名等）</li>
          <li>租赁记录</li>
          <li>支付信息</li>
        </ul>

        <h4>2. 信息使用</h4>
        <p>我们使用收集的信息用于：</p>
        <ul>
          <li>提供租赁服务</li>
          <li>客户支持</li>
          <li>服务改进</li>
        </ul>

        <h4>3. 信息保护</h4>
        <p>我们采用行业标准的安全措施保护您的个人信息。</p>

        <h4>4. 信息共享</h4>
        <p>除法律要求外，我们不会与第三方共享您的个人信息。</p>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import {ref, computed, onMounted} from 'vue'
import {useRouter} from 'vue-router'
import {ElMessage} from 'element-plus'
import {User, Lock} from '@element-plus/icons-vue'
//@ts-ignore
import {register} from '@/services/userApi.ts'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const registerFormRef = ref()
const showAgreementDialog = ref(false)
const showPrivacyDialog = ref(false)

// 注册表单
const registerForm = ref({
  userAccount: '',
  userPassword: '',
  userCheckPassword: '',
  agreement: false
})


// 表单验证规则
const registerFormRules = {
  userAccount: [
    {required: true, message: '请输入用户名', trigger: 'blur'},
  ],
  userPassword: [
    {required: true, message: '请输入密码', trigger: 'blur'},
  ],
  userCheckPassword: [
    {required: true, message: '请确认密码', trigger: 'blur'},
    {
      validator: (_rule: any, value: string, callback: Function) => {
        if (value !== registerForm.value.userPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  agreement: [
    {
      validator: (_rule: any, value: boolean, callback: Function) => {
        if (!value) {
          callback(new Error('请阅读并同意用户协议'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ]
}


// 表单是否有效
const isFormValid = computed(() => {
  return registerForm.value.userAccount &&
      registerForm.value.userPassword &&
      registerForm.value.userCheckPassword &&
      registerForm.value.agreement
})

// 注册处理
const handleRegister = async () => {
  try {

    loading.value = true

    const response = await register({...registerForm.value});
    if (response.code !== 0) {
      ElMessage.error(response.description || '注册失败');
    }
    if (response.code === 0) {
      ElMessage.success('注册成功！')
      // 跳转到登录页
      setTimeout(() => {
        router.push('/login')
      }, 1500)
    }
  } catch (error) {
    console.error('注册错误:', error)
  } finally {
    loading.value = false
  }
}

// 显示用户协议
const showAgreement = () => {
  showAgreementDialog.value = true
}

// 显示隐私政策
const showPrivacy = () => {
  showPrivacyDialog.value = true
}

// 跳转登录
const goToLogin = () => {
  router.push('/login')
}

onMounted(() => {
  // 页面初始化
})
</script>

<style scoped>
.register-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.geometric-shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.shape-1 {
  width: 100px;
  height: 100px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 10%;
  animation-delay: 2s;
}

.shape-3 {
  width: 80px;
  height: 80px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

.register-card {
  width: 380px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 40px;
  position: relative;
  z-index: 1;
}

.card-header {
  text-align: center;
  margin-bottom: 32px;
}

.logo {
  font-size: 28px;
  font-weight: 700;
  color: #409EFF;
  margin: 0 0 8px 0;
}

.subtitle {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

.register-form {
  margin-top: 24px;
}

.agreement-container {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.agreement-container :deep(.el-checkbox) {
  margin-right: 4px;
}

.agreement-text {
  line-height: 1.4;
  margin-left: 4px;
}

.agreement-text :deep(.el-link) {
  line-height: inherit !important;
  vertical-align: baseline !important;
  display: inline !important;
}

.register-button {
  width: 100%;
  height: 44px;
  font-size: 16px;
  font-weight: 500;
}

.card-footer {
  text-align: center;
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}

.login-link {
  margin: 0;
  font-size: 14px;
  color: #606266;
}

.agreement-content,
.privacy-content {
  max-height: 400px;
  overflow-y: auto;
  line-height: 1.6;
}

.agreement-content h3,
.privacy-content h3 {
  color: #409EFF;
  margin-bottom: 16px;
}

.agreement-content h4,
.privacy-content h4 {
  color: #303133;
  margin: 16px 0 8px 0;
}

.agreement-content p,
.privacy-content p {
  margin-bottom: 12px;
  color: #606266;
}

.agreement-content ul,
.privacy-content ul {
  margin: 8px 0;
  padding-left: 20px;
}

.agreement-content li,
.privacy-content li {
  margin-bottom: 4px;
  color: #606266;
}

</style> 
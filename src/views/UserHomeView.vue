<template>
  <div class="car-rental-home">
    <el-container>
      <el-header>
        <!-- 导航栏 -->
        <nav class="navbar">
          <div class="nav-container">
            <h1 class="nav-title">智能汽车租赁系统</h1>
            <div class="nav-buttons">
              <el-button :type="currentPage === 'home' ? 'primary' : 'default'" @click="switchPage('home')">
                主页
              </el-button>
              <el-button :type="currentPage === 'orders' ? 'primary' : 'default'" @click="switchPage('orders')">
                我的订单
              </el-button>
              <el-dropdown @command="handleDropdownCommand">
            <span class="user-info">
              <el-avatar 
                :size="32" 
                :src="userinfo?.avatarUrl || ''"
                :icon="!userinfo?.avatarUrl ? 'UserFilled' : undefined"
              />
              <span class="username">{{ userinfo?.username || '用户' }}</span>
              <el-icon><ArrowDown/></el-icon>
            </span>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="profile">
                      <el-icon>
                        <User/>
                      </el-icon>
                      个人信息
                    </el-dropdown-item>
                    <el-dropdown-item command="password">
                      <el-icon>
                        <Lock/>
                      </el-icon>
                      修改密码
                    </el-dropdown-item>
                    <el-dropdown-item divided command="logout">
                      <el-icon>
                        <SwitchButton/>
                      </el-icon>
                      退出登录
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </nav>
      </el-header>
      <el-main>
        <router-view/>
      </el-main>
    </el-container>

    <!-- 个人信息对话框 -->
    <el-dialog v-model="showProfileDialog" title="个人信息" width="600px">
      <div class="profile-content">
        <!-- 头像部分 -->
        <div class="avatar-section">
          <h3>头像设置</h3>
          <div class="avatar-upload">
            <div class="avatar-preview">
              <el-avatar 
                :size="100" 
                :src="tempAvatarUrl || userinfo?.avatarUrl || ''"
                :icon="!tempAvatarUrl && !userinfo?.avatarUrl ? 'UserFilled' : undefined"
              />
            </div>
            <div class="upload-actions">
              <el-button type="primary" @click="triggerAvatarUpload">
                <el-icon><Camera/></el-icon>
                选择图片
              </el-button>
              <input
                ref="avatarInput"
                type="file"
                accept="image/*"
                style="display: none"
                @change="handleAvatarChange"
              />
            </div>
            <div class="upload-tips">
              <p>支持 JPG、PNG、GIF 格式，文件大小不超过 5MB</p>
            </div>
          </div>
        </div>

        <!-- 基本信息部分 -->
        <div class="basic-info-section">
          <h3>基本信息</h3>
          <el-form :model="profileForm" label-width="80px">
            <el-form-item label="用户名">
              <el-input v-model="profileForm.username" placeholder="请输入用户名"/>
            </el-form-item>
            <el-form-item label="邮箱">
              <el-input v-model="profileForm.email" type="email" placeholder="请输入邮箱"/>
            </el-form-item>
            <el-form-item label="手机号">
              <el-input v-model="profileForm.phone" placeholder="请输入手机号"/>
            </el-form-item>
            <el-form-item label="性别">
              <el-select v-model="profileForm.gender" placeholder="请选择性别">
                <el-option label="未知" :value="0"/>
                <el-option label="男" :value="1"/>
                <el-option label="女" :value="2"/>
              </el-select>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showProfileDialog = false">取消</el-button>
          <el-button type="primary" @click="saveProfile" :loading="profileLoading">
            保存
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 修改密码对话框 -->
    <el-dialog v-model="showPasswordDialog" title="修改密码" width="500px">
      <el-form :model="passwordForm" label-width="100px">
        <el-form-item label="当前密码">
          <el-input 
            v-model="passwordForm.oldPassword" 
            type="password" 
            placeholder="请输入当前密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="新密码">
          <el-input 
            v-model="passwordForm.newPassword" 
            type="password" 
            placeholder="请输入新密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="确认新密码">
          <el-input 
            v-model="passwordForm.confirmPassword" 
            type="password" 
            placeholder="请再次输入新密码"
            show-password
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showPasswordDialog = false">取消</el-button>
          <el-button type="primary" @click="handleChangePassword" :loading="passwordLoading">
            修改密码
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import {computed, ref} from 'vue'
import {ElMessage} from 'element-plus'
import {ArrowDown, SwitchButton, Camera, Lock, User} from '@element-plus/icons-vue'
import {useRouter, useRoute} from 'vue-router'
import {getUserInfo, userLogout} from '@/utils/auth'
import {uploadFile} from '@/services/file'
import {changePassword, updateUser, type UpdateUserParams} from '@/services/userApi'

// 路由相关
const router = useRouter()
const route = useRoute()

// 对话框状态
const showProfileDialog = ref(false)
const showPasswordDialog = ref(false)
const profileLoading = ref(false)
const passwordLoading = ref(false)

// 头像相关
const avatarInput = ref<HTMLInputElement | null>(null)
const tempAvatarUrl = ref('')
const selectedFile = ref<File | null>(null)

// 个人信息表单
const profileForm = ref({
  username: '',
  email: '',
  phone: '',
  gender: 0
})

// 密码表单
const passwordForm = ref({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 导航相关逻辑
const currentPage = computed(() => {
  if (route.path === '/user/home') return 'home'
  if (route.path === '/user/orders') return 'orders'
  return 'home'
})

const switchPage = (page: 'home' | 'orders') => {
  if (page === 'home') {
    router.push('/user/home')
  } else if (page === 'orders') {
    router.push('/user/orders')
  }
}

// 下拉菜单处理
const handleDropdownCommand = (command: string) => {
  switch (command) {
    case 'profile':
      loadProfileForm()
      showProfileDialog.value = true
      break
    case 'password':
      showPasswordDialog.value = true
      break
    case 'logout':
      handleUserLogout()
      break
  }
}

// 加载个人信息表单
const loadProfileForm = () => {
  const userFromStorage = getUserInfo()
  if (userFromStorage) {
    profileForm.value = {
      username: userFromStorage.username || '',
      email: userFromStorage.email || '',
      phone: userFromStorage.phone || '',
      gender: userFromStorage.gender || 0
    }
  }
}

// 用户登出
const handleUserLogout = () => {
  userLogout()
  ElMessage.success('已退出登录')
  router.push('/login')
}

// 用户信息状态
const userinfo = ref(getUserInfo())

// 更新用户信息的函数
const updateUserInfo = () => {
  userinfo.value = getUserInfo()
}

// 头像上传相关
const triggerAvatarUpload = () => {
  avatarInput.value?.click()
}

const handleAvatarChange = (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0]
  if (!file) return

  // 验证文件类型
  if (!file.type.startsWith('image/')) {
    ElMessage.error('请选择图片文件')
    return
  }

  // 验证文件大小（限制为5MB）
  if (file.size > 5 * 1024 * 1024) {
    ElMessage.error('图片大小不能超过5MB')
    return
  }

  // 创建临时URL预览
  tempAvatarUrl.value = URL.createObjectURL(file)
  selectedFile.value = file
}

const saveProfile = async () => {
  profileLoading.value = true
  try {
    const userFromStorage = getUserInfo()
    if (!userFromStorage) {
      ElMessage.error('用户信息不存在')
      return
    }

    // 如果有新头像，先上传头像
    let avatarUrl = userFromStorage.avatarUrl
    if (selectedFile.value) {
      const uploadResponse = await uploadFile(selectedFile.value)
      if (uploadResponse.code === 0) {
        avatarUrl = uploadResponse.data
      } else {
        ElMessage.error(uploadResponse.description || '头像上传失败')
        return
      }
    }

    // 更新用户信息
    const updateParams: UpdateUserParams = {
      id: userFromStorage.id,
      username: profileForm.value.username,
      email: profileForm.value.email,
      phone: profileForm.value.phone,
      gender: profileForm.value.gender,
      avatarUrl: avatarUrl
    }
    
    const updateResponse = await updateUser(updateParams)
    if (updateResponse.code === 0) {
      // 更新本地存储
      const updatedUserInfo = { ...userFromStorage, ...updateParams }
      sessionStorage.setItem('userinfo', JSON.stringify(updatedUserInfo))
      // 更新响应式状态
      updateUserInfo()
      ElMessage.success('个人信息保存成功')
      showProfileDialog.value = false
      // 清理临时数据
      tempAvatarUrl.value = ''
      selectedFile.value = null
    } else {
      ElMessage.error(updateResponse.description || '个人信息保存失败')
    }
  } catch (error) {
    ElMessage.error('个人信息保存失败')
    console.error('保存个人信息错误:', error)
  } finally {
    profileLoading.value = false
  }
}

// 修改密码
const handleChangePassword = async () => {
  // 验证密码
  if (passwordForm.value.newPassword !== passwordForm.value.confirmPassword) {
    ElMessage.error('两次输入的密码不一致')
    return
  }
  
  if (passwordForm.value.newPassword.length < 8) {
    ElMessage.error('新密码长度不能少于8位')
    return
  }

  passwordLoading.value = true
  try {
    const userFromStorage = getUserInfo()
    if (!userFromStorage) {
      ElMessage.error('用户信息不存在')
      return
    }

    const changePasswordParams = {
      id: userFromStorage.id,
      oldPassword: passwordForm.value.oldPassword,
      newPassword: passwordForm.value.newPassword,
      confirmPassword: passwordForm.value.confirmPassword
    }

    const response = await changePassword(changePasswordParams)
    if (response.code === 0) {
      ElMessage.success('密码修改成功')
      showPasswordDialog.value = false
      // 清空表单
      passwordForm.value = {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      }
    } else {
      ElMessage.error(response.description || '密码修改失败')
    }
  } catch (error) {
    ElMessage.error('密码修改失败')
    console.error('修改密码错误:', error)
  } finally {
    passwordLoading.value = false
  }
}

</script>

<style scoped>
.car-rental-home {
  min-height: 100vh;
  background-color: #f5f7fa;
}

/* 导航栏样式 */
.navbar {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  height: 80px;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-title {
  font-family: 'Poppins', sans-serif;
  font-size: 24px;
  font-weight: 600;
  color: #409EFF;
  margin: 0;
}

.nav-buttons {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.3s;
  color: #333;
}

.user-info:hover {
  background: rgba(64, 158, 255, 0.1);
}

.username {
  font-size: 14px;
  color: #333;
}

/* 个人信息对话框样式 */
.profile-content {
  padding: 20px 0;
}

.avatar-section,
.basic-info-section {
  margin-bottom: 30px;
}

.avatar-section h3,
.basic-info-section h3 {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #409EFF;
}

.avatar-upload {
  text-align: center;
  padding: 20px 0;
}

.avatar-preview {
  margin-bottom: 20px;
}

.upload-actions {
  margin-bottom: 16px;
}

.upload-tips {
  color: #909399;
  font-size: 12px;
}

.upload-tips p {
  margin: 0;
}

/* 对话框底部按钮样式 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

</style> 
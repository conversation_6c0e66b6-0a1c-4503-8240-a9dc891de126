<template>
  <div class="login-page">
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="geometric-shape shape-1"></div>
      <div class="geometric-shape shape-2"></div>
      <div class="geometric-shape shape-3"></div>
    </div>

    <!-- 登录卡片 -->
    <div class="login-card">
      <div class="card-header">
        <h1 class="logo">汽车租赁系统</h1>
        <p class="subtitle">智能出行，轻松租赁</p>
      </div>

      <!-- 标签页切换 -->
      <el-tabs v-model="activeTab" @tab-change="handleTabChange" class="login-tabs">
        <el-tab-pane label="账号登录" name="account">
          <el-form
              ref="accountFormRef"
              :model="accountForm"
              :rules="accountFormRules"
              class="login-form"
          >
            <el-form-item prop="userAccount">
              <el-input
                  v-model="accountForm.userAccount"
                  placeholder="请输入账号"
                  size="large"
                  :prefix-icon="User"
              />
            </el-form-item>

            <el-form-item prop="userPassword">
              <el-input
                  v-model="accountForm.userPassword"
                  type="password"
                  placeholder="请输入密码"
                  size="large"
                  :prefix-icon="Lock"
                  show-password
              />
            </el-form-item>

            <el-form-item prop="captcha">
              <div class="captcha-container">
                <el-input
                    v-model="accountForm.captcha"
                    placeholder="请输入验证码"
                    size="large"
                    :prefix-icon="Key"
                    @keyup.enter="handleAccountLogin"
                />
                <div class="captcha-image-container">
                  <img
                      v-if="captchaImage"
                      :src="captchaImage"
                      alt="验证码"
                      class="captcha-image"
                      @click="refreshCaptcha"
                  />
                  <div v-else class="captcha-loading">
                    <el-icon class="is-loading">
                      <Loading/>
                    </el-icon>
                  </div>
                </div>
              </div>
            </el-form-item>

            <el-form-item>
              <div class="form-options">
                <el-checkbox v-model="accountForm.remember">记住密码</el-checkbox>
                <el-link type="primary" @click="handleForgotPassword">忘记密码？</el-link>
              </div>
            </el-form-item>

            <el-form-item>
              <el-button
                  type="primary"
                  size="large"
                  :loading="loading"
                  @click="handleAccountLogin"
                  class="login-button"
              >
                登录
              </el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <el-tab-pane label="邮箱登录" name="email">
          <el-form
              ref="emailFormRef"
              :model="emailForm"
              :rules="emailFormRules"
              class="login-form"
          >
            <el-form-item prop="email">
              <el-input
                  v-model="emailForm.email"
                  placeholder="请输入邮箱地址"
                  size="large"
                  :prefix-icon="Message"
                  @keyup.enter="handleEmailLogin"
              />
            </el-form-item>

            <el-form-item prop="code">
              <div class="captcha-container">
                <el-input
                    v-model="emailForm.code"
                    placeholder="请输入邮箱验证码"
                    size="large"
                    :prefix-icon="Key"
                />
                <el-button
                    type="primary"
                    :disabled="emailCountdown > 0"
                    @click="sendEmailCaptcha"
                    class="send-captcha-btn"
                >
                  {{ emailCountdown > 0 ? `${emailCountdown}s` : '获取验证码' }}
                </el-button>
              </div>
            </el-form-item>

            <el-form-item>
              <div class="form-options">
                <el-checkbox v-model="emailForm.remember">记住密码</el-checkbox>
                <el-link type="primary" @click="handleForgotPassword">忘记密码？</el-link>
              </div>
            </el-form-item>

            <el-form-item>
              <el-button
                  type="primary"
                  size="large"
                  :loading="loading"
                  @click="handleEmailLogin"
                  class="login-button"
              >
                登录
              </el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>

      <!-- 底部链接 -->
      <div class="card-footer">
        <p class="register-link">
          还没有账号？
          <el-link type="primary" @click="goToRegister">立即注册</el-link>
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {onMounted, ref} from 'vue'
import {useRouter} from 'vue-router'
import {ElMessage} from 'element-plus'
import {Key, Loading, Lock, Message, User} from '@element-plus/icons-vue'
import {login, sendSimpleMail, emailLogin, getCode} from '@/services/userApi.ts'

const router = useRouter()

// 响应式数据
const activeTab = ref('account')
const loading = ref(false)
const accountFormRef = ref()
const emailFormRef = ref()
const captchaImage = ref('')
const emailCountdown = ref(0)
const codeId = ref('')

// 账号登录表单
const accountForm = ref({
  userAccount: '',
  userPassword: '',
  captcha: '',
  remember: false
})

// 邮箱登录表单
const emailForm = ref({
  email: '',
  code: '',
  remember: false
})

// 表单验证规则
const accountFormRules = {
  userAccount: [
    {required: true, message: '请输入账号', trigger: 'blur'},
  ],
  userPassword: [
    {required: true, message: '请输入密码', trigger: 'blur'},
  ],
  captcha: [
    {required: true, message: '请输入验证码', trigger: 'blur'},
    {len: 4, message: '验证码长度为 4 位', trigger: 'blur'}
  ]
}

const emailFormRules = {
  email: [
    {required: true, message: '请输入邮箱地址', trigger: 'blur'},
    {type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur'}
  ],
  code: [
    {len: 6, message: '验证码长度为 6 位', trigger: 'blur'}
  ]
}

// 获取后端验证码图片
const getCaptchaImage = async () => {
  try {
    const response = await getCode(codeId.value)
    if (response.code === 0) {
      captchaImage.value = response.data.base64Code
      codeId.value = response.data.codeId
      accountForm.value.captcha = ''
    } else {
      ElMessage.error('获取验证码失败')
    }
  } catch (error) {
    console.error('获取验证码失败:', error)
    ElMessage.error('获取验证码失败')
  }
}

// 刷新验证码
const refreshCaptcha = () => {
  getCaptchaImage()
}

// 发送邮箱验证码
const sendEmailCaptcha = async () => {
  try {
    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(emailForm.value.email)) {
      ElMessage.error('请输入正确的邮箱格式')
      return
    }
    const response = await sendSimpleMail(emailForm.value.email)
    if (response.code === 0) {
      ElMessage.success('验证码已发送到您的邮箱')

      // 开始倒计时
      emailCountdown.value = 60
      const timer = setInterval(() => {
        emailCountdown.value--
        if (emailCountdown.value <= 0) {
          clearInterval(timer)
        }
      }, 1000)
    } else {
      ElMessage.error(response.description || '发送失败')
    }
  } catch (error) {
    console.error('发送验证码失败:', error)
    ElMessage.error('发送验证码失败')
  }
}

// 标签页切换
const handleTabChange = () => {
  // 清空表单
  accountForm.value = {
    userAccount: '',
    userPassword: '',
    captcha: '',
    remember: false
  }
  emailForm.value = {
    email: '',
    code: '',
    remember: false
  }

  // 重置倒计时
  emailCountdown.value = 0

  // 刷新验证码
  if (activeTab.value === 'account') {
    refreshCaptcha()
  }
}


// 账号登录
const handleAccountLogin = async () => {
  try {
    await accountFormRef.value.validate()

    loading.value = true
    const response = await login({
      ...accountForm.value,
      codeId: codeId.value
    })
    if (response.code === 0) {
      ElMessage.success('登录成功')

      // 保存登录状态
      sessionStorage.setItem('userinfo', JSON.stringify(response.data))
      if (response.data.userRole === 0) {
        await router.push('/user')
      } else if (response.data.userRole === 1) {
        // 跳转到首页
        await router.push('/admin')
      }

    } else {
      ElMessage.error(response.description || '登录失败')
      refreshCaptcha()
    }
  } catch (error) {
    console.error('登录错误:', error)
    ElMessage.error('登录失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 邮箱登录
const handleEmailLogin = async () => {
  try {
    await emailFormRef.value.validate()

    loading.value = true

    const response = await emailLogin({
      email: emailForm.value.email,
      code: emailForm.value.code
    })
    if (response.code === 0) {
      ElMessage.success('登录成功')

      // 保存登录状态
      sessionStorage.setItem('userinfo', JSON.stringify(response.data))
      if (response.data.userRole === 0) {
        await router.push('/user')
      } else if (response.data.userRole === 1) {
        await router.push('/admin')
      }
    } else {
      ElMessage.error(response.description || '邮箱登录失败')
    }
  } catch (error) {
    console.error('邮箱登录错误:', error)
    ElMessage.error('邮箱登录失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 忘记密码
const handleForgotPassword = () => {
  ElMessage.info('请联系客服重置密码')
}

// 跳转注册
const goToRegister = () => {
  router.push('/register')
}

onMounted(() => {
  // 初始化验证码
  refreshCaptcha()
})
</script>

<style scoped>
.login-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.geometric-shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.shape-1 {
  width: 100px;
  height: 100px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 10%;
  animation-delay: 2s;
}

.shape-3 {
  width: 80px;
  height: 80px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

.login-card {
  width: 380px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 40px;
  position: relative;
  z-index: 1;
}

.card-header {
  text-align: center;
  margin-bottom: 32px;
}

.logo {
  font-size: 28px;
  font-weight: 700;
  color: #409EFF;
  margin: 0 0 8px 0;
}

.subtitle {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

.login-tabs {
  margin-bottom: 24px;
}

.login-form {
  margin-top: 24px;
}

.captcha-container {
  display: flex;
  gap: 12px;
  align-items: center;
}

.captcha-image-container {
  width: 120px;
  height: 40px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.captcha-image-container:hover {
  border-color: #409EFF;
  box-shadow: 0 0 8px rgba(64, 158, 255, 0.2);
}

.captcha-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.captcha-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  color: #909399;
}

.send-captcha-btn {
  width: 120px;
  height: 40px;
  font-size: 12px;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.login-button {
  width: 100%;
  height: 44px;
  font-size: 16px;
  font-weight: 500;
}

.card-footer {
  text-align: center;
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}

.register-link {
  margin: 0;
  font-size: 14px;
  color: #606266;
}

</style> 
<template>
  <div class="admin-layout">
    <!-- 顶部导航 -->
    <header class="admin-header">
      <div class="header-left">
        <h1 class="logo">汽车租赁管理系统</h1>
      </div>
      <div class="header-right">
        <el-dropdown>
          <span class="user-info">
            <el-avatar 
              :size="32" 
              :src="userinfo?.avatarUrl || ''"
              :icon="!userinfo?.avatarUrl ? 'UserFilled' : undefined"
            />
            <span class="username">{{ userinfo?.username || '管理员' }}</span>
            <el-icon><ArrowDown/></el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="goToProfile">
                <el-icon>
                  <User/>
                </el-icon>
                个人中心
              </el-dropdown-item>
              <el-dropdown-item @click="handleLogout">
                <el-icon>
                  <SwitchButton/>
                </el-icon>
                退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </header>

    <!-- 主体内容 -->
    <div class="admin-main">
      <!-- 左侧菜单 -->
      <aside class="admin-sidebar">
        <el-menu
            :default-active="activeMenu"
            :collapse-transition="true"
            class="sidebar-menu"
            background-color="#1f2d3d"
            text-color="#fff"
            active-text-color="#409EFF"
            router
        >
          <!-- 动态生成菜单 -->
          <template v-for="menu in menus" :key="menu.id">
            <!-- 有子菜单的情况 -->
            <el-sub-menu v-if="menu.children && menu.children.length > 0" :index="`sub-${menu.id}`">
              <template #title>
                <el-icon>
                  <component :is="getMenuIcon(menu.menuName)"/>
                </el-icon>
                <span>{{ menu.menuName }}</span>
              </template>
              <el-menu-item
                  v-for="child in menu.children"
                  :key="child.id"
                  :index="child.route"
              >
                <el-icon>
                  <component :is="getMenuIcon(child.menuName)"/>
                </el-icon>
                <span>{{ child.menuName }}</span>
              </el-menu-item>
            </el-sub-menu>

            <!-- 没有子菜单的情况 -->
            <el-menu-item v-else :index="menu.route">
              <el-icon>
                <component :is="getMenuIcon(menu.menuName)"/>
              </el-icon>
              <span>{{ menu.menuName }}</span>
            </el-menu-item>
          </template>
        </el-menu>
      </aside>

      <!-- 主内容区 -->
      <main class="admin-content">
        <!-- 面包屑导航 -->
        <div class="breadcrumb-section">
          <el-breadcrumb separator="/">
            <el-breadcrumb-item
                v-for="(item, index) in breadcrumbs"
                :key="index"
                :to="item.path"
            >
              {{ item.title }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>

        <!-- 页面内容 -->
        <div class="content-wrapper">
          <router-view/>
        </div>
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import {ref, computed, onMounted} from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {ElMessage, ElMessageBox} from 'element-plus'
import {
  ArrowDown,
  SwitchButton,
  DataBoard,
  User,
  Van,
  Document,
  Setting,
} from '@element-plus/icons-vue'
import {getUserInfo, userLogout} from '../utils/auth.ts'
import request from '@/services/request/request.ts'

const route = useRoute()
const router = useRouter()

// 菜单接口类型定义
interface Menu {
  id: number;
  pid: number;
  menuName: string;
  route: string;
  createTime: string;
  updateTime: string;
  children: Menu[] | null;
}

// 响应式数据
const menus = ref<Menu[]>([])

// 当前激活的菜单
const activeMenu = computed(() => route.path)

// 面包屑导航
const breadcrumbs = computed(() => {
  const paths = route.path.split('/').filter(Boolean)
  const breadcrumbItems: Array<{ path: string; title: string }> = []

  let currentPath = ''
  paths.forEach((path, _index) => {
    currentPath += `/${path}`
    let title: string

    switch (path) {
      case 'admin':
        title = '管理后台'
        break
      case 'dashboard':
        title = '数据概览'
        break
      case 'cars':
        title = '车辆管理'
        break
      case 'orders':
        title = '订单管理'
        break
      case 'users':
        title = '用户管理'
        break
      case 'settings':
        title = '系统设置'
        break
      case 'profile':
        title = '个人中心'
        break
      default:
        title = path
    }

    breadcrumbItems.push({
      path: currentPath,
      title
    })
  })

  return breadcrumbItems
})

// 跳转到个人中心
const goToProfile = () => {
  router.push('/admin/profile')
}

// 退出登录
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm(
        '确定要退出登录吗？',
        '确认退出',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
    )
    userLogout();
    ElMessage.success('已退出登录')
    await router.push('/login')
  } catch (error) {
    // 用户取消退出
  }
}

// 管理员信息状态
const userinfo = ref(getUserInfo())

// 更新管理员信息的函数
const updateUserInfo = () => {
  userinfo.value = getUserInfo()
}

// 获取菜单数据
const getMenus = async () => {
  try {
    const userFromStorage = JSON.parse(sessionStorage.getItem('userinfo') || '{}')
    const userId = userFromStorage.id

    const response = await request.get('/api/menu/getMenu', {
      params: {userId: userId}
    })

    if (response.data.code === 0) {
      menus.value = response.data.data
      console.log('获取到的菜单数据:', menus.value)
    } else {
      menus.value = []
      console.error('获取菜单失败:', response.data.description)
    }
  } catch (error) {
    console.error('获取菜单失败:', error)
    menus.value = []
  }
}

// 根据菜单名称获取图标组件
const getMenuIcon = (menuName: string) => {
  switch (menuName) {
    case '仪表盘':
      return DataBoard
    case '用户管理':
      return User
    case '车辆管理':
      return Van
    case '个人中心':
      return User
    case '订单管理':
      return Document
    case '系统设置':
      return Setting
    default:
      return Document
  }
}

// 页面初始化
onMounted(async () => {
  await getMenus()
})
</script>

<style scoped>
.admin-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.admin-header {
  height: 60px;
  background: #1f2d3d;
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.header-left .logo {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
  color: #409EFF;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background: rgba(255, 255, 255, 0.1);
}

.username {
  font-size: 14px;
  color: white;
}

.admin-main {
  flex: 1;
  display: flex;
}

.admin-sidebar {
  width: 240px;
  background: #1f2d3d;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
}

.sidebar-menu {
  border: none;
  height: 100%;
}

.admin-content {
  flex: 1;
  background: #f5f7fa;
  display: flex;
  flex-direction: column;
}

.breadcrumb-section {
  background: white;
  padding: 16px 24px;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.content-wrapper {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .admin-sidebar {
    width: 200px;
  }

  .content-wrapper {
    padding: 16px;
  }

  .admin-header {
    padding: 0 16px;
  }

  .header-left .logo {
    font-size: 18px;
  }
}

@media (max-width: 480px) {
  .admin-sidebar {
    width: 180px;
  }

  .content-wrapper {
    padding: 12px;
  }
}
</style> 
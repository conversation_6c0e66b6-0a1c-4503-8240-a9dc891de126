import {createApp} from 'vue'
import {createPinia} from 'pinia'


import App from './App.vue'
import ElementPlus from 'element-plus'
import * as Icons from "@element-plus/icons-vue";
import 'element-plus/dist/index.css'
import router from './router'

const app = createApp(App)

for (const [key, component] of Object.entries(Icons)) {
    app.component(key, component)
}
app.use(createPinia())
app.use(router)
app.use(ElementPlus)


app.mount('#app')
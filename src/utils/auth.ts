import type {LoginRes} from "@/services/userApi.ts";

interface LoginUser {
    /* */
    id: number;

    /* */
    username: string;

    /* */
    userAccount: string;

    /* */
    avatarUrl: string;

    /* */
    gender: number;

    /* */
    userPassword: string;

    /* */
    phone: string;

    /* */
    email: string;

    /* */
    userStatus: number;

    /* */
    createTime: Record<string, unknown>;

    /* */
    updateTime: Record<string, unknown>;

    /* */
    isDelete: number;

    /* */
    userRole: number;

    /* */
    lastLoginTime: Record<string, unknown>;
}

// 获取用户信息
export const getUserInfo = (): LoginUser | null => {
    const userInfoStr = sessionStorage.getItem('userinfo')
    if (userInfoStr) {
        try {
            return JSON.parse(userInfoStr)
        } catch (error) {
            console.error('解析用户信息失败:', error)
            return null
        }
    }
    return null
}
// 用户登出
export const userLogout = (): void => {
    sessionStorage.removeItem('userinfo')
}


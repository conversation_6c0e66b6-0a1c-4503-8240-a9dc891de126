# 智能汽车租赁系统前端

一个基于Vue3 + Element Plus的现代化汽车租赁系统前端界面。

## 功能特性

- 🚗 **汽车展示**：网格化卡片布局展示汽车信息
- 🎠 **轮播图**：动态轮播展示推广内容
- 💳 **租赁功能**：一键租赁汽车
- 📄 **详情页面**：点击卡片跳转到汽车详情页
- 📅 **日期选择**：详情页支持选择租赁日期
- 💰 **价格计算**：自动计算租赁天数和总费用
- 📋 **订单管理**：生成订单、查看订单列表和详情
- 💳 **支付功能**：支持订单支付和取消
- 📊 **状态管理**：订单状态（待支付、已支付、已完成、已取消）
- 📱 **响应式设计**：支持桌面端和移动端
- 🎨 **现代化UI**：使用Element Plus组件库
- ⚡ **TypeScript**：完整的类型支持

## 技术栈

- **前端框架**: Vue 3 + Composition API
- **UI组件库**: Element Plus 3
- **数据交互**: Axios
- **开发语言**: TypeScript
- **构建工具**: Vite

## 项目结构

```
src/
├── components/          # Vue组件
│   ├── car/            # 汽车相关组件
│   │   ├── CarCard.vue     # 汽车卡片组件
│   │   ├── CarDetail.vue   # 汽车详情页组件
│   │   └── CarSearchFilter.vue # 汽车搜索筛选组件
│   ├── order/          # 订单相关组件
│   │   ├── OrderList.vue   # 订单列表组件
│   │   └── OrderDetail.vue # 订单详情组件
│   ├── admin/          # 管理员组件
│   ├── user/           # 用户组件
│   └── DemoPage.vue    # 演示页面组件
├── services/           # API服务
│   ├── carApi.ts       # 汽车相关API
│   ├── ordersApi.ts    # 订单相关API
│   ├── userApi.ts      # 用户相关API
│   └── bannerApi.ts    # 轮播图相关API
├── router/            # 路由配置
│   └── index.ts       # 路由定义
├── stores/            # 状态管理
├── App.vue            # 根组件
└── main.ts            # 入口文件
```

## 快速开始

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

访问 http://localhost:5173 查看应用

### 构建生产版本

```bash
npm run build
```

## 页面路由

- `/` - 主页（汽车列表）
- `/demo` - 演示页面
- `/car/:id` - 汽车详情页（:id为汽车ID）
- `/orders` - 订单列表页
- `/order/:id` - 订单详情页（:id为订单ID）

## 组件说明

### car/CarCard.vue
汽车卡片组件，包含：
- 汽车图片占位区（渐变色背景 + 车辆图标）
- 汽车基本信息（名称、参数、价格）
- 租赁按钮（带加载状态）

### car/CarDetail.vue
汽车详情页组件，包含：
- 汽车图片展示（主图 + 缩略图）
- 详细的汽车参数信息
- 租赁日期选择器
- 自动计算租赁天数和总费用
- 租赁表单验证
- 生成订单功能

### car/CarSearchFilter.vue
汽车搜索筛选组件，包含：
- 多种筛选条件（品牌、座位数、变速箱等）
- 价格范围选择
- 年份筛选
- 搜索和重置功能

### order/OrderList.vue
订单列表页面组件，包含：
- 用户所有订单展示
- 订单状态筛选
- 订单支付和取消功能
- 跳转到订单详情

### order/OrderDetail.vue
订单详情页面组件，包含：
- 订单详细信息展示
- 汽车信息展示
- 租赁信息展示
- 订单支付和取消功能

### carApi.ts
汽车相关API服务，提供：
- 汽车数据查询
- 汽车管理功能
- 搜索和筛选功能

### ordersApi.ts
订单相关API服务，提供：
- 订单数据查询
- 订单状态管理
- 订单CRUD操作

### userApi.ts
用户相关API服务，提供：
- 用户数据查询
- 用户管理功能

### bannerApi.ts
轮播图相关API服务，提供：
- 轮播图数据管理
- 轮播图CRUD操作

## 设计规范

### 配色方案
- **主色调**: 科技蓝 (#409EFF)
- **辅助色**: 活力橙 (#FF6700)
- **背景色**: 浅灰 (#f5f7fa)

### 字体规范
- **标题**: Poppins 字体（Google Fonts）
- **正文**: 系统默认无衬线字体

### 间距系统
- **基准**: 8px
- **卡片间距**: 16px
- **内边距**: 12px

## 响应式设计

- **桌面端**: 4列网格布局
- **平板端**: 自适应2-3列
- **移动端**: 单列布局

## 开发说明

### 添加新汽车
通过管理员界面的车辆管理功能添加新汽车，或直接调用 `carApi.ts` 中的相关API。

### 自定义轮播图
通过管理员界面的轮播图管理功能自定义轮播图，或直接调用 `bannerApi.ts` 中的相关API。

### API集成
所有API服务都已集成真实的后端接口，支持完整的CRUD操作。

### 订单功能
- 租赁汽车时自动生成订单
- 支持订单状态管理（待支付、已支付、已完成、已取消）
- 提供订单支付和取消功能
- 订单列表支持状态筛选

## 浏览器支持

- Chrome >= 87
- Firefox >= 78
- Safari >= 14
- Edge >= 88

## 许可证

MIT License

# 支付宝支付流程修改说明

## 问题描述
原来的支付流程在打开支付宝支付页面后立即调用了回调接口，没有等待用户实际完成支付就处理了支付结果。

## 修改内容

### 1. 修改支付流程逻辑
- **原流程**: 打开支付页面 → 立即调用 `/api/alipay/notify` → 处理支付结果
- **新流程**: 打开支付页面 → 显示状态检查对话框 → 轮询检查支付状态 → 处理支付结果

### 2. 新增轮询机制
```javascript
// 轮询检查支付状态
const checkPaymentStatus = async (orderId, formattedDate, paymentMethod) => {
  // 最多检查60次，每次间隔5秒，总共5分钟
  // 调用 /api/alipay/status 接口检查支付状态
  // 根据返回结果处理：success/failed/pending
}
```

### 3. 新增用户界面改进
- 添加支付状态检查对话框
- 显示轮询进度 (当前次数/总次数)
- 提供用户友好的状态提示

### 4. 需要后端配合的接口
需要后端提供 `/api/alipay/status` 接口：
- 输入参数: `orderNo` (订单号)
- 返回值: 
  - `'success'` - 支付成功
  - `'failed'` - 支付失败  
  - `'pending'` - 支付中/未支付

### 5. 修改的文件
- `src/components/order/OrderDetail.vue`
  - 修改支付宝支付逻辑
  - 添加轮询状态检查函数
  - 添加状态检查对话框UI
  - 添加相关样式

## 使用说明
1. 用户点击支付宝支付
2. 系统打开支付宝支付页面
3. 显示"支付状态检查"对话框
4. 系统每5秒检查一次支付状态
5. 用户在支付宝页面完成支付后，系统自动检测到并更新订单状态
6. 最多检查5分钟，超时提示用户手动刷新

## 优势
- 真正等待用户完成支付
- 提供实时的状态反馈
- 避免支付状态不一致的问题
- 用户体验更好

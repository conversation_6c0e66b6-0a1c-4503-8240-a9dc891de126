# 支付宝支付流程修改说明

## 问题描述
原来的支付流程在打开支付宝支付页面后立即调用了回调接口，没有等待用户实际完成支付就处理了支付结果。

## 修改内容

### 1. 修改支付流程逻辑
- **原流程**: 打开支付页面 → 立即调用 `/api/alipay/notify` → 处理支付结果
- **新流程**: 打开支付页面 → 显示状态检查对话框 → 轮询检查支付状态 → 处理支付结果

### 2. 新增轮询机制
```javascript
// 轮询检查支付状态
const checkPaymentStatus = async (orderId, formattedDate, paymentMethod) => {
  // 最多检查60次，每次间隔5秒，总共5分钟
  // 调用 /api/alipay/status 接口检查支付状态
  // 根据返回结果处理：success/failed/pending
}
```

### 3. 新增用户界面改进
- 添加支付状态检查对话框
- 显示轮询进度 (当前次数/总次数)
- 提供用户友好的状态提示

### 4. 后端接口说明

#### 现有问题
- `/api/alipay/notify` 是 `@PostMapping`，但前端错误地使用了 GET 请求
- 这个接口是支付宝的异步回调接口，不应该被前端直接调用

#### 解决方案
**方案1: 使用现有订单查询接口（推荐）**
- 前端通过轮询 `/api/order/page` 接口检查订单状态
- 当订单状态从 `pending` 变为 `paid` 时，说明支付成功
- 这种方式不需要新增后端接口

**方案2: 新增专门的支付状态查询接口**
```java
@GetMapping("/query")
public String queryPaymentStatus(@RequestParam String orderNo) {
    // 查询订单支付状态
    // 返回: "success", "failed", "pending"
}
```

### 5. 修改的文件
- `src/components/order/OrderDetail.vue`
  - 修改支付宝支付逻辑
  - 添加轮询状态检查函数
  - 添加状态检查对话框UI
  - 添加相关样式

## 使用说明
1. 用户点击支付宝支付
2. 系统调用 `/api/alipay/pay` 获取支付页面并打开
3. 显示"支付状态检查"对话框
4. 系统每5秒通过 `/api/order/page` 查询订单状态
5. 用户在支付宝页面完成支付后，支付宝会异步回调 `/api/alipay/notify` 更新订单状态
6. 前端检测到订单状态变为 `paid` 后，显示支付成功
7. 最多检查5分钟，超时提示用户手动刷新

## 完整的支付流程
```
用户点击支付
    ↓
前端调用 /api/alipay/pay 获取支付页面
    ↓
打开支付宝支付页面
    ↓
用户在支付宝完成支付
    ↓
支付宝异步回调 /api/alipay/notify (POST)
    ↓
后端更新订单状态为 paid
    ↓
前端轮询检测到订单状态变化
    ↓
显示支付成功，更新UI
```

## 优势
- 真正等待用户完成支付
- 提供实时的状态反馈
- 避免支付状态不一致的问题
- 用户体验更好
